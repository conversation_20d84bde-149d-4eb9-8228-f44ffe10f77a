/**
 * Featured Badge Styles
 * أنماط مختلفة لشارات المزادات المميزة
 */

// أنواع مختلفة من الشارات
export const BADGE_STYLES = {
    CROWN: 'crown',
    STAR: 'star', 
    DIAMOND: 'diamond',
    PREMIUM: 'premium',
    VIP: 'vip'
};

// إنشاء HTML للشارة حسب النوع
export const createFeaturedBadgeHTML = (style = BADGE_STYLES.CROWN, options = {}) => {
    const {
        text = 'مميز',
        position = 'top-left',
        animated = true
    } = options;

    const animationClass = animated ? 'animated' : '';
    
    switch (style) {
        case BADGE_STYLES.CROWN:
            return `
                <div class="featured-badge crown-style ${animationClass}">
                    <span class="featured-icon">👑</span>
                    <span class="featured-text">${text}</span>
                </div>
            `;
            
        case BADGE_STYLES.STAR:
            return `
                <div class="featured-badge star-style ${animationClass}">
                    <div class="featured-content">
                        <span class="featured-icon">⭐</span>
                        <span class="featured-text">${text}</span>
                    </div>
                </div>
            `;
            
        case BADGE_STYLES.DIAMOND:
            return `
                <div class="featured-badge diamond-style ${animationClass}">
                    <div class="featured-content">
                        <span class="featured-icon">💎</span>
                        <span class="featured-text">${text}</span>
                    </div>
                </div>
            `;
            
        case BADGE_STYLES.PREMIUM:
            return `
                <div class="featured-badge premium-style ${animationClass}">
                    <span class="featured-icon">🏆</span>
                    <span class="featured-text">${text}</span>
                </div>
            `;
            
        case BADGE_STYLES.VIP:
            return `
                <div class="featured-badge vip-style ${animationClass}">
                    <span class="featured-icon">🌟</span>
                    <span class="featured-text">${text}</span>
                </div>
            `;
            
        default:
            return createFeaturedBadgeHTML(BADGE_STYLES.CROWN, options);
    }
};

// إنشاء React component للشارة
export const FeaturedBadge = ({ 
    style = BADGE_STYLES.CROWN, 
    text = 'مميز', 
    animated = true,
    className = '' 
}) => {
    const animationClass = animated ? 'animated' : '';
    const badgeClasses = `featured-badge ${style}-style ${animationClass} ${className}`.trim();
    
    const renderBadgeContent = () => {
        switch (style) {
            case BADGE_STYLES.CROWN:
                return (
                    <>
                        <span className="featured-icon">👑</span>
                        <span className="featured-text">{text}</span>
                    </>
                );
                
            case BADGE_STYLES.STAR:
                return (
                    <div className="featured-content">
                        <span className="featured-icon">⭐</span>
                        <span className="featured-text">{text}</span>
                    </div>
                );
                
            case BADGE_STYLES.DIAMOND:
                return (
                    <div className="featured-content">
                        <span className="featured-icon">💎</span>
                        <span className="featured-text">{text}</span>
                    </div>
                );
                
            case BADGE_STYLES.PREMIUM:
                return (
                    <>
                        <span className="featured-icon">🏆</span>
                        <span className="featured-text">{text}</span>
                    </>
                );
                
            case BADGE_STYLES.VIP:
                return (
                    <>
                        <span className="featured-icon">🌟</span>
                        <span className="featured-text">{text}</span>
                    </>
                );
                
            default:
                return (
                    <>
                        <span className="featured-icon">👑</span>
                        <span className="featured-text">{text}</span>
                    </>
                );
        }
    };
    
    return (
        <div className={badgeClasses}>
            {renderBadgeContent()}
        </div>
    );
};

// CSS classes للأنماط المختلفة
export const getBadgeClasses = (style, animated = true) => {
    const baseClass = 'featured-badge';
    const styleClass = `${style}-style`;
    const animationClass = animated ? 'animated' : '';
    
    return [baseClass, styleClass, animationClass].filter(Boolean).join(' ');
};

// تحديد النمط المناسب حسب نوع المزاد
export const getBadgeStyleByAuctionType = (auctionType) => {
    const typeMap = {
        'إلكتروني': BADGE_STYLES.CROWN,
        'حضوري': BADGE_STYLES.PREMIUM,
        'هجين': BADGE_STYLES.VIP
    };
    
    return typeMap[auctionType] || BADGE_STYLES.CROWN;
};

// ألوان مخصصة للأنماط
export const BADGE_COLORS = {
    [BADGE_STYLES.CROWN]: {
        primary: '#ff6b35',
        secondary: '#f7931e',
        text: '#ffffff'
    },
    [BADGE_STYLES.STAR]: {
        primary: '#ffd700',
        secondary: '#ffed4e', 
        text: '#1a1a1a'
    },
    [BADGE_STYLES.DIAMOND]: {
        primary: '#667eea',
        secondary: '#764ba2',
        text: '#ffffff'
    },
    [BADGE_STYLES.PREMIUM]: {
        primary: '#ff9a56',
        secondary: '#ff6b35',
        text: '#ffffff'
    },
    [BADGE_STYLES.VIP]: {
        primary: '#a8edea',
        secondary: '#fed6e3',
        text: '#1a1a1a'
    }
};

// إنشاء CSS متغيرات للألوان
export const generateBadgeCSS = (style) => {
    const colors = BADGE_COLORS[style];
    if (!colors) return '';
    
    return `
        --badge-primary: ${colors.primary};
        --badge-secondary: ${colors.secondary};
        --badge-text: ${colors.text};
    `;
};

export default {
    BADGE_STYLES,
    createFeaturedBadgeHTML,
    FeaturedBadge,
    getBadgeClasses,
    getBadgeStyleByAuctionType,
    BADGE_COLORS,
    generateBadgeCSS
};
