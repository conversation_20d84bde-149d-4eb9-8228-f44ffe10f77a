# دليل المزادات المميزة الشامل - Featured Auctions 🌟

## نظرة عامة
خاصية المزادات المميزة تتيح للمستخدمين تمييز مزادات معينة وعرضها في المقدمة مع شارة مائلة أنيقة وتصميم مميز.

## ✨ المميزات الرئيسية
- 🎀 شارة مائلة أنيقة (110px × 30px)
- 🎨 تدرج لوني برتقالي جذاب
- ✨ تأثيرات بصرية متقدمة (حركة وإضاءة)
- 📱 تصميم متجاوب لجميع الأجهزة
- 🔍 واجهة بحث ذكية لاختيار المزادات
- 📌 المزادات المميزة تظهر دائماً في المقدمة

## الملفات المضافة

### 1. `FeaturedAuctionsManager.js`
- **الوظيفة**: مدير واجهة اختيار المزادات المميزة
- **المميزات**:
  - بحث ذكي في المزادات
  - اختيار متعدد مع معاينة
  - إدارة سهلة للمزادات المختارة
  - واجهة مستخدم احترافية

### 2. `FeaturedAuctionsManager.css`
- **الوظيفة**: تصميم واجهة مدير المزادات المميزة
- **المميزات**:
  - تصميم متجاوب
  - ألوان ذهبية للمزادات المميزة
  - تأثيرات بصرية جذابة
  - دعم إمكانية الوصول

### 3. `FeaturedAuctionsLogic.js`
- **الوظيفة**: منطق إدارة وترتيب المزادات المميزة
- **الوظائف الرئيسية**:
  - `separateAuctions()`: فصل المزادات إلى مميزة وعادية
  - `validateFeaturedAuctions()`: التحقق من صحة المزادات المختارة
  - `getAuctionCardClasses()`: إنشاء CSS classes للمزادات
  - `getFeaturedBadgeHTML()`: إنشاء شارة المزاد المميز

### 4. `FeaturedAuctions.css`
- **الوظيفة**: تصميم المزادات المميزة في الواجهة الأمامية
- **المميزات**:
  - إطار ذهبي للمزادات المميزة
  - شارة "مميز" مع نجمة
  - تأثيرات الإضاءة والظلال
  - تصميم متجاوب ومتاح للجميع

## التحديثات على الملفات الموجودة

### `block.json`
```json
{
  "enableFeaturedAuctions": {
    "type": "boolean",
    "default": false
  }
}
```

### `SettingsPanel.js`
- إضافة استيراد `FeaturedAuctionsManager`
- دمج مدير المزادات المميزة في الإعدادات

### `edit.js`
- استيراد `separateAuctions` و `getAuctionCardClasses`
- تطبيق منطق فصل المزادات المميزة
- عرض معلومات المزادات المميزة

### `save.js`
- حفظ بيانات المزادات المميزة في data attributes
- عرض معاينة للمزادات المميزة

### `view.js`
- إضافة دعم المزادات المميزة في الواجهة الأمامية
- تطبيق منطق الفصل والترتيب
- إضافة شارات المزادات المميزة

### `Card.js`
- إضافة دعم خاصية `isFeatured`
- عرض شارة المزاد المميز
- تطبيق CSS classes المناسبة

### `style.scss`
- استيراد ملف `FeaturedAuctions.css`

## كيفية الاستخدام

### 1. تفعيل الخاصية
1. افتح محرر الصفحة/المقال
2. أضف أو حدد بلوك "Amlak Auctions Block"
3. في الشريط الجانبي، ابحث عن قسم "🌟 المزادات المميزة"
4. فعّل خيار "تفعيل المزادات المميزة"

### 2. اختيار المزادات المميزة
1. استخدم شريط البحث للعثور على المزادات المطلوبة
2. حدد المزادات التي تريد تمييزها
3. ستظهر المزادات المختارة في قسم "المزادات المميزة المختارة"
4. يمكنك إزالة أي مزاد بالضغط على زر "✕"

### 3. النتيجة
- المزادات المميزة ستظهر في الصف الأول دائماً
- ستحصل على إطار ذهبي وشارة "مميز ⭐"
- تأثيرات بصرية جذابة مع الإضاءة الذهبية

## المميزات التقنية

### البحث الذكي
- البحث في العنوان، المدينة، نوع المزاد، والوكيل
- نتائج فورية أثناء الكتابة
- عرض معلومات مفصلة لكل مزاد

### الأداء
- تحميل المزادات عند الحاجة فقط
- فصل منطق المزادات المميزة في ملفات منفصلة
- تحسين الذاكرة والسرعة

### إمكانية الوصول
- دعم لوحة المفاتيح
- ألوان متباينة للمستخدمين ذوي الاحتياجات الخاصة
- دعم قارئات الشاشة

### التصميم المتجاوب
- يعمل على جميع أحجام الشاشات
- تكيف تلقائي للهواتف والأجهزة اللوحية
- تحسين للطباعة

## استكشاف الأخطاء

### المزادات لا تظهر كمميزة
1. تأكد من تفعيل خاصية "المزادات المميزة"
2. تحقق من اختيار المزادات في الإعدادات
3. احفظ الصفحة وأعد تحميلها

### البحث لا يعمل
1. تأكد من وجود اتصال بالإنترنت
2. تحقق من عمل REST API للمزادات
3. راجع console المتصفح للأخطاء

### التصميم لا يظهر صحيحاً
1. تأكد من بناء البلاجن بـ `npm run build`
2. امسح cache المتصفح
3. تحقق من تحميل ملفات CSS

## التطوير المستقبلي

### مميزات مقترحة
- إعادة ترتيب المزادات المميزة بالسحب والإفلات
- تحديد عدد أقصى للمزادات المميزة
- جدولة المزادات المميزة (تفعيل/إلغاء تلقائي)
- إحصائيات المزادات المميزة

### تحسينات تقنية
- تخزين مؤقت للمزادات المتاحة
- تحميل تدريجي للمزادات الكثيرة
- دعم المزادات المميزة في REST API

## الدعم
للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
