/**
 * React hook that is used to mark the block wrapper element.
 * It provides all the necessary props like the class name.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-block-editor/#useblockprops
 */
import { useBlockProps } from '@wordpress/block-editor';

/**
 * The save function defines the way in which the different attributes should
 * be combined into the final markup, which is then serialized by the block
 * editor into `post_content`.
 *
 * For dynamic blocks that fetch data from the database, we return null
 * and handle rendering in PHP via render_callback.
 *
 * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-edit-save/#save
 *
 * @return {Element} Element to render.
 */
export default function save({ attributes }) {
	// For dynamic blocks, we save the attributes as data attributes
	// and let the frontend JavaScript handle the rendering
	return (
		<div
			{ ...useBlockProps.save() }
			data-columns={attributes.columns}
			data-auctions-count={attributes.auctionsCount}
			data-show-images={attributes.showImages}
			data-show-title={attributes.showTitle}
			data-show-type={attributes.showType}
			data-show-location={attributes.showLocation}
			data-show-assets-count={attributes.showAssetsCount}
			data-show-date-time={attributes.showDateTime}
			data-show-countdown={attributes.showCountdown}
			data-show-agent={attributes.showAgent}
		>
			<div className="amlak-auctions-placeholder">
				<p>جاري تحميل المزادات...</p>
			</div>
		</div>
	);
}
