/* Featured Auctions Styles */

/* Container for auctions with featured info */
.amlak-auctions-container {
    position: relative;
}

/* Featured auctions info banner */
.featured-auctions-info {
    background: linear-gradient(135deg, #fff9e6, #fef3c7);
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    text-align: center;
}

.featured-info-text {
    margin: 0;
    color: #92400e;
    font-size: 14px;
    font-weight: 500;
}

/* Featured auction card styles */
.featured-auction-card {
    position: relative;
    border: 2px solid #ffd700 !important;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3) !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff, #fffef7);
}

.featured-auction-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4) !important;
}

/* Featured badge */
.featured-badge {
    position: absolute;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 4px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a1a;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border: 1px solid #f59e0b;
}

.featured-badge-top-right {
    top: 8px;
    right: 8px;
}

.featured-badge-top-left {
    top: 8px;
    left: 8px;
}

.featured-icon {
    font-size: 12px;
    line-height: 1;
}

.featured-text {
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Featured auction in first row */
.featured-first-row {
    background: linear-gradient(135deg, #ffffff, #fffbf0);
}

/* Animation for featured auctions */
@keyframes featuredGlow {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    }
    50% {
        box-shadow: 0 4px 16px rgba(255, 215, 0, 0.5);
    }
}

.featured-auction-card {
    animation: featuredGlow 3s ease-in-out infinite;
}

/* Featured auction type badge enhancement */
.featured-auction-card .auction-type-badge {
    border: 1px solid #ffd700;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.2);
}

/* Featured auction title enhancement */
.featured-auction-card .auction-title-new {
    color: #1a1a1a;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(255, 215, 0, 0.1);
}

/* Featured auction info items enhancement */
.featured-auction-card .auction-info-item {
    background: rgba(255, 215, 0, 0.05);
    border-radius: 6px;
    padding: 8px;
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.featured-auction-card .auction-info-item .icon svg {
    filter: drop-shadow(0 1px 2px rgba(255, 215, 0, 0.3));
}

/* Featured auction countdown enhancement */
.featured-auction-card .countdown-timer {
    background: linear-gradient(135deg, #fff9e6, #fef3c7);
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 12px;
}

.featured-auction-card .countdown-item {
    background: #ffffff;
    border: 1px solid #ffd700;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.1);
}

.featured-auction-card .countdown-number {
    color: #92400e;
    font-weight: 700;
}

.featured-auction-card .countdown-label {
    color: #a16207;
}

/* Featured auction click overlay enhancement */
.featured-auction-card .card-click-overlay {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 237, 78, 0.9));
}

.featured-auction-card .click-indicator {
    color: #1a1a1a;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

/* Featured placeholder in editor */
.featured-placeholder {
    color: #92400e;
    font-size: 12px;
    font-style: italic;
    margin: 4px 0 0 0;
}

/* Responsive design for featured auctions */
@media (max-width: 768px) {
    .featured-badge {
        padding: 3px 6px;
        font-size: 10px;
    }
    
    .featured-icon {
        font-size: 11px;
    }
    
    .featured-text {
        font-size: 9px;
    }
    
    .featured-auction-card {
        transform: translateY(-1px);
    }
    
    .featured-auction-card:hover {
        transform: translateY(-2px);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .featured-auction-card {
        border-color: #000000 !important;
        background: #ffffff !important;
    }
    
    .featured-badge {
        background: #000000 !important;
        color: #ffffff !important;
        border-color: #000000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .featured-auction-card {
        animation: none;
        transform: none;
    }
    
    .featured-auction-card:hover {
        transform: none;
    }
}

/* Print styles */
@media print {
    .featured-auction-card {
        border: 2px solid #000000 !important;
        box-shadow: none !important;
        transform: none !important;
        animation: none !important;
    }
    
    .featured-badge {
        background: #000000 !important;
        color: #ffffff !important;
    }
    
    .featured-auctions-info {
        background: #f5f5f5 !important;
        border: 1px solid #000000 !important;
    }
}

/* Focus styles for accessibility */
.featured-auction-card:focus {
    outline: 3px solid #ffd700;
    outline-offset: 2px;
}

.featured-auction-card:focus-visible {
    outline: 3px solid #ffd700;
    outline-offset: 2px;
}

/* Loading state for featured auctions */
.featured-auction-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.featured-auction-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}
