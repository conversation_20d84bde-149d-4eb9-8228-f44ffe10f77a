/* Featured Auctions Styles */

/* Container for auctions with featured info */
.amlak-auctions-container {
    position: relative;
}

/* Featured auctions info banner */
.featured-auctions-info {
    background: linear-gradient(135deg, #fff9e6, #fef3c7);
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    text-align: center;
}

.featured-info-text {
    margin: 0;
    color: #92400e;
    font-size: 14px;
    font-weight: 500;
}

/* Featured auction card styles - إزالة الإطار في الباك إند */
.wp-block-create-block-amlak-auctions-block .featured-auction-card {
    position: relative;
    border: none !important; /* إزالة الإطار */
    box-shadow: none !important; /* إزالة الظل */
    transform: none !important; /* إزالة الحركة */
    transition: none !important;
    background: transparent !important; /* خلفية شفافة */
    overflow: visible;
}

.wp-block-create-block-amlak-auctions-block .featured-auction-card:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* تطبيق التصميم المميز فقط في الواجهة الأمامية */
.amlak-auctions-grid .featured-auction-card {
    border: 2px solid #ff6b35 !important;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3) !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff, #fffaf7);
}

.amlak-auctions-grid .featured-auction-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(255, 107, 53, 0.4) !important;
}

/* التأكد من أن الشارة تظهر فوق جميع العناصر */
.featured-auction-card .auction-img-wrapper {
    position: relative;
    overflow: visible;
}

/* إصلاح ارتفاع الكارت في الباك إند */
.wp-block-create-block-amlak-auctions-block .amlak-auction-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

.wp-block-create-block-amlak-auctions-block .amlak-auction-card > * {
    height: auto !important;
}

/* Featured badge - شارة أنيقة طولية للمزادات المميزة */
.featured-badge {
    position: absolute;
    z-index: 10;
    top: 8px;
    left: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 80px;
    background: linear-gradient(180deg, #d4af37, #ffd700, #d4af37);
    color: #1a1a1a;
    border-radius: 14px;
    font-size: 10px;
    font-weight: 800;
    box-shadow:
        0 4px 12px rgba(212, 175, 55, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #b8860b;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    transform: translateX(-2px);
}

.featured-badge:hover {
    transform: translateX(0px) scale(1.05);
    box-shadow:
        0 6px 16px rgba(212, 175, 55, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.15);
}

/* أيقونة ونص الشارة الطولية */
.featured-icon {
    font-size: 16px;
    line-height: 1;
    margin-bottom: 4px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.featured-text {
    font-size: 8px;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    writing-mode: vertical-rl;
    text-orientation: mixed;
    line-height: 1.2;
}

/* تأثيرات إضافية للشارة الطولية */
.featured-badge::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(180deg, #ffd700, #d4af37, #ffd700);
    border-radius: 15px;
    z-index: -1;
    opacity: 0.8;
    animation: goldGlow 3s ease-in-out infinite alternate;
}

@keyframes goldGlow {
    0% {
        opacity: 0.8;
        box-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
    }
    100% {
        opacity: 1;
        box-shadow: 0 0 15px rgba(212, 175, 55, 0.6);
    }
}

/* تأثير لمعان الذهب */
.featured-badge::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.8), transparent);
    border-radius: 2px;
    animation: shine 2s ease-in-out infinite;
}

@keyframes shine {
    0%, 100% {
        opacity: 0.3;
        transform: translateY(0);
    }
    50% {
        opacity: 0.8;
        transform: translateY(40px);
    }
}

/* شارة بديلة - تصميم الماس */
.featured-badge.diamond-style {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 8px;
    transform: rotate(45deg);
    width: 40px;
    height: 40px;
    justify-content: center;
    padding: 0;
}

.featured-badge.diamond-style .featured-content {
    transform: rotate(-45deg);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.featured-badge.diamond-style .featured-icon {
    font-size: 12px;
}

.featured-badge.diamond-style .featured-text {
    font-size: 8px;
    line-height: 1;
}

/* شارة بديلة - تصميم النجمة */
.featured-badge.star-style {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a1a;
    border: 2px solid #f59e0b;
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    width: 45px;
    height: 45px;
    padding: 0;
    justify-content: center;
    transform: rotate(0deg);
}

.featured-badge.star-style .featured-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1px;
    margin-top: 8px;
}

.featured-badge.star-style .featured-icon {
    font-size: 10px;
}

.featured-badge.star-style .featured-text {
    font-size: 7px;
    line-height: 1;
}

/* تأثيرات الحركة للشارة الجديدة */
.featured-badge {
    animation: gentlePulse 4s ease-in-out infinite;
}

@keyframes gentlePulse {
    0%, 100% {
        transform: translateX(-2px) scale(1);
    }
    50% {
        transform: translateX(-1px) scale(1.02);
    }
}

/* Featured auction in first row */
.featured-first-row {
    background: linear-gradient(135deg, #ffffff, #fffbf0);
}

/* Animation for featured auctions */
@keyframes featuredGlow {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    }
    50% {
        box-shadow: 0 4px 16px rgba(255, 215, 0, 0.5);
    }
}

.featured-auction-card {
    animation: featuredGlow 3s ease-in-out infinite;
}

/* Featured auction type badge enhancement */
.featured-auction-card .auction-type-badge {
    border: 1px solid #ffd700;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.2);
}

/* Featured auction title enhancement */
.featured-auction-card .auction-title-new {
    color: #1a1a1a;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(255, 215, 0, 0.1);
}

/* Featured auction info items enhancement */
.featured-auction-card .auction-info-item {
    background: rgba(255, 215, 0, 0.05);
    border-radius: 6px;
    padding: 8px;
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.featured-auction-card .auction-info-item .icon svg {
    filter: drop-shadow(0 1px 2px rgba(255, 215, 0, 0.3));
}

/* Featured auction countdown enhancement */
.featured-auction-card .countdown-timer {
    background: linear-gradient(135deg, #fff9e6, #fef3c7);
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 12px;
}

.featured-auction-card .countdown-item {
    background: #ffffff;
    border: 1px solid #ffd700;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.1);
}

.featured-auction-card .countdown-number {
    color: #92400e;
    font-weight: 700;
}

.featured-auction-card .countdown-label {
    color: #a16207;
}

/* Featured auction click overlay enhancement */
.featured-auction-card .card-click-overlay {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 237, 78, 0.9));
}

.featured-auction-card .click-indicator {
    color: #1a1a1a;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

/* Featured placeholder in editor */
.featured-placeholder {
    color: #92400e;
    font-size: 12px;
    font-style: italic;
    margin: 4px 0 0 0;
}

/* Responsive design for featured auctions */
@media (max-width: 768px) {
    .featured-badge {
        width: 24px;
        height: 60px;
        border-radius: 12px;
        top: 6px;
        left: 6px;
    }

    .featured-icon {
        font-size: 14px;
        margin-bottom: 2px;
    }

    .featured-text {
        font-size: 7px;
        letter-spacing: 0.3px;
    }

    .featured-badge::after {
        width: 3px;
        height: 15px;
    }
}

@media (max-width: 480px) {
    .featured-badge {
        width: 20px;
        height: 50px;
        border-radius: 10px;
    }

    .featured-icon {
        font-size: 12px;
    }

    .featured-text {
        font-size: 6px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .featured-auction-card {
        border-color: #000000 !important;
        background: #ffffff !important;
    }
    
    .featured-badge {
        background: #000000 !important;
        color: #ffffff !important;
        border-color: #000000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .featured-auction-card {
        animation: none;
        transform: none;
    }
    
    .featured-auction-card:hover {
        transform: none;
    }
}

/* Print styles */
@media print {
    .featured-auction-card {
        border: 2px solid #000000 !important;
        box-shadow: none !important;
        transform: none !important;
        animation: none !important;
    }
    
    .featured-badge {
        background: #000000 !important;
        color: #ffffff !important;
    }
    
    .featured-auctions-info {
        background: #f5f5f5 !important;
        border: 1px solid #000000 !important;
    }
}

/* Focus styles for accessibility */
.featured-auction-card:focus {
    outline: 3px solid #ffd700;
    outline-offset: 2px;
}

.featured-auction-card:focus-visible {
    outline: 3px solid #ffd700;
    outline-offset: 2px;
}

/* Loading state for featured auctions */
.featured-auction-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.featured-auction-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}
