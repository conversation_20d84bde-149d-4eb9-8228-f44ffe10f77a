/* Featured Auctions Styles */

/* Container for auctions with featured info */
.amlak-auctions-container {
    position: relative;
}

/* Featured auctions info banner */
.featured-auctions-info {
    background: linear-gradient(135deg, #fff9e6, #fef3c7);
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    text-align: center;
}

.featured-info-text {
    margin: 0;
    color: #92400e;
    font-size: 14px;
    font-weight: 500;
}

/* Featured auction card styles */
.featured-auction-card {
    position: relative;
    border: 2px solid #ff6b35 !important;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3) !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff, #fffaf7);
    overflow: visible; /* للسماح للشارة بالظهور خارج حدود الكارت */
}

.featured-auction-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(255, 107, 53, 0.4) !important;
}

/* التأكد من أن الشارة تظهر فوق جميع العناصر */
.featured-auction-card .auction-img-wrapper {
    position: relative;
    overflow: visible;
}

/* Featured badge - شارة احترافية للمزادات المميزة */
.featured-badge {
    position: absolute;
    z-index: 10;
    top: 12px;
    left: 12px; /* على الجانب المقابل لشارة نوع المزاد */
    display: flex;
    align-items: center;
    gap: 6px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    box-shadow: 0 3px 10px rgba(255, 107, 53, 0.4);
    border: 2px solid #ffffff;
    backdrop-filter: blur(10px);
    transform: rotate(-5deg);
    transition: all 0.3s ease;
}

.featured-badge:hover {
    transform: rotate(0deg) scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.6);
}

/* أيقونة التاج للمزادات المميزة */
.featured-icon {
    font-size: 14px;
    line-height: 1;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.featured-text {
    font-size: 11px;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* تأثيرات إضافية للشارة */
.featured-badge::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff6b35, #f7931e, #ff6b35);
    border-radius: 22px;
    z-index: -1;
    opacity: 0.7;
    animation: borderGlow 2s ease-in-out infinite alternate;
}

@keyframes borderGlow {
    0% {
        opacity: 0.7;
        transform: scale(1);
    }
    100% {
        opacity: 1;
        transform: scale(1.02);
    }
}

/* شارة بديلة - تصميم الماس */
.featured-badge.diamond-style {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 8px;
    transform: rotate(45deg);
    width: 40px;
    height: 40px;
    justify-content: center;
    padding: 0;
}

.featured-badge.diamond-style .featured-content {
    transform: rotate(-45deg);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.featured-badge.diamond-style .featured-icon {
    font-size: 12px;
}

.featured-badge.diamond-style .featured-text {
    font-size: 8px;
    line-height: 1;
}

/* شارة بديلة - تصميم النجمة */
.featured-badge.star-style {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a1a;
    border: 2px solid #f59e0b;
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    width: 45px;
    height: 45px;
    padding: 0;
    justify-content: center;
    transform: rotate(0deg);
}

.featured-badge.star-style .featured-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1px;
    margin-top: 8px;
}

.featured-badge.star-style .featured-icon {
    font-size: 10px;
}

.featured-badge.star-style .featured-text {
    font-size: 7px;
    line-height: 1;
}

/* شارة التاج - النمط الافتراضي المحسن */
.featured-badge.crown-style {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    border: 2px solid #ffffff;
    box-shadow: 0 3px 10px rgba(255, 107, 53, 0.4);
}

.featured-badge.crown-style:hover {
    background: linear-gradient(135deg, #f7931e, #ff6b35);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.6);
}

/* شارة البريميوم */
.featured-badge.premium-style {
    background: linear-gradient(135deg, #ff9a56, #ff6b35);
    color: white;
    border: 2px solid #ffd700;
    box-shadow: 0 3px 10px rgba(255, 154, 86, 0.4);
}

/* شارة VIP */
.featured-badge.vip-style {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
    color: #1a1a1a;
    border: 2px solid #667eea;
    box-shadow: 0 3px 10px rgba(168, 237, 234, 0.4);
}

/* تأثيرات الحركة */
.featured-badge.animated {
    animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
    0%, 100% {
        transform: rotate(-5deg) scale(1);
    }
    50% {
        transform: rotate(-3deg) scale(1.05);
    }
}

.featured-badge.animated:hover {
    animation: badgeHover 0.3s ease-in-out forwards;
}

@keyframes badgeHover {
    0% {
        transform: rotate(-5deg) scale(1);
    }
    100% {
        transform: rotate(0deg) scale(1.1);
    }
}

/* Featured auction in first row */
.featured-first-row {
    background: linear-gradient(135deg, #ffffff, #fffbf0);
}

/* Animation for featured auctions */
@keyframes featuredGlow {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    }
    50% {
        box-shadow: 0 4px 16px rgba(255, 215, 0, 0.5);
    }
}

.featured-auction-card {
    animation: featuredGlow 3s ease-in-out infinite;
}

/* Featured auction type badge enhancement */
.featured-auction-card .auction-type-badge {
    border: 1px solid #ffd700;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.2);
}

/* Featured auction title enhancement */
.featured-auction-card .auction-title-new {
    color: #1a1a1a;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(255, 215, 0, 0.1);
}

/* Featured auction info items enhancement */
.featured-auction-card .auction-info-item {
    background: rgba(255, 215, 0, 0.05);
    border-radius: 6px;
    padding: 8px;
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.featured-auction-card .auction-info-item .icon svg {
    filter: drop-shadow(0 1px 2px rgba(255, 215, 0, 0.3));
}

/* Featured auction countdown enhancement */
.featured-auction-card .countdown-timer {
    background: linear-gradient(135deg, #fff9e6, #fef3c7);
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 12px;
}

.featured-auction-card .countdown-item {
    background: #ffffff;
    border: 1px solid #ffd700;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.1);
}

.featured-auction-card .countdown-number {
    color: #92400e;
    font-weight: 700;
}

.featured-auction-card .countdown-label {
    color: #a16207;
}

/* Featured auction click overlay enhancement */
.featured-auction-card .card-click-overlay {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 237, 78, 0.9));
}

.featured-auction-card .click-indicator {
    color: #1a1a1a;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

/* Featured placeholder in editor */
.featured-placeholder {
    color: #92400e;
    font-size: 12px;
    font-style: italic;
    margin: 4px 0 0 0;
}

/* Responsive design for featured auctions */
@media (max-width: 768px) {
    .featured-badge {
        padding: 3px 6px;
        font-size: 10px;
    }
    
    .featured-icon {
        font-size: 11px;
    }
    
    .featured-text {
        font-size: 9px;
    }
    
    .featured-auction-card {
        transform: translateY(-1px);
    }
    
    .featured-auction-card:hover {
        transform: translateY(-2px);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .featured-auction-card {
        border-color: #000000 !important;
        background: #ffffff !important;
    }
    
    .featured-badge {
        background: #000000 !important;
        color: #ffffff !important;
        border-color: #000000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .featured-auction-card {
        animation: none;
        transform: none;
    }
    
    .featured-auction-card:hover {
        transform: none;
    }
}

/* Print styles */
@media print {
    .featured-auction-card {
        border: 2px solid #000000 !important;
        box-shadow: none !important;
        transform: none !important;
        animation: none !important;
    }
    
    .featured-badge {
        background: #000000 !important;
        color: #ffffff !important;
    }
    
    .featured-auctions-info {
        background: #f5f5f5 !important;
        border: 1px solid #000000 !important;
    }
}

/* Focus styles for accessibility */
.featured-auction-card:focus {
    outline: 3px solid #ffd700;
    outline-offset: 2px;
}

.featured-auction-card:focus-visible {
    outline: 3px solid #ffd700;
    outline-offset: 2px;
}

/* Loading state for featured auctions */
.featured-auction-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.featured-auction-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}
