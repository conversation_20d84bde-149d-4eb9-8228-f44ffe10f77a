.amlak-auction-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  padding: 0 0 18px 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: stretch;
  min-height: 370px;
  transition: box-shadow 0.2s;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  direction: rtl;
}
.amlak-auction-card:hover {
  box-shadow: 0 8px 32px rgba(0,0,0,0.16);
  border-color: #e0e0e0;
}
.auction-title-agent-row {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 14px 18px 8px 18px;
  margin: 0;
  direction: rtl;
}
.auction-title-new {
  font-size: 1.18em;
  font-weight: bold;
  color: #1a2233;
  line-height: 1.4;
  margin: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}
.auction-agent-new {
  background: #BB1919;
  color: #fff;
  font-size: 0.95em;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 8px;
  display: inline-block;
  letter-spacing: 0.01em;
  align-self: flex-start;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.3;
  text-align: center;
}
.auction-type, .auction-location, .auction-assets-count, .auction-date-time, .auction-agent {
  font-size: 0.97em;
  color: #555;
  margin: 0 16px 2px 16px;
  line-height: 1.7;
}
.amlak-img-slider {
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  min-height: 180px;
}
.amlak-auction-card .slider-img {
  min-height: 180px;
  max-height: 200px;
  object-fit: cover;
  width: 100%;
}
.amlak-countdown {
  margin: 8px 16px 0 16px;
}
.amlak-auction-card .auction-btn {
  margin: 16px;
  margin-top: 10px;
  background: #007cba;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 0;
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
  width: 100%;
}
.amlak-auction-card .auction-btn:hover {
  background: #005fa3;
}

.auction-img-wrapper {
  position: relative;
}
.auction-type-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: stretch;
  border-radius: 8px;
  z-index: 2;
  box-shadow: none;
  border: none;
  overflow: hidden;
}
.badge-icon-bg {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
}
.badge-icon-bg-إلكتروني { background: #2196f3; }
.badge-icon-bg-حضوري { background: #43a047; }
.badge-icon-bg-هجين { background: #8e44ad; }
.badge-icon-large {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.badge-icon-large svg {
  width: 22px;
  height: 22px;
  display: block;
  color: #fff;
  fill: #fff;
}
.badge-text-bg {
  background: #f7f7f7;
  color: #222;
  font-size: 1.08em;
  font-weight: 600;
  display: flex;
  align-items: center;
  padding: 0 16px 0 12px;
  letter-spacing: 0.01em;
}
.auction-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 10px;
  margin: 10px 16px 0 16px;
}
.auction-info-item {
  display: flex;
  align-items: center;
  font-size: 0.97em;
  color: #444;
  background: #f8fafd;
  border-radius: 6px;
  padding: 6px 10px 6px 6px;
  gap: 6px;
  min-width: 0;
  word-break: break-word;
}
.auction-info-item .icon {
  display: flex;
  align-items: center;
  margin-left: 4px;
}
