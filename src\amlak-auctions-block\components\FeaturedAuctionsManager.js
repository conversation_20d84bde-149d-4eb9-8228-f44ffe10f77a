import React, { useState, useEffect } from 'react';
import { 
    PanelBody, 
    ToggleControl, 
    TextControl, 
    CheckboxControl, 
    Button,
    Spinner 
} from '@wordpress/components';
import apiFetch from '@wordpress/api-fetch';
import '../styles/FeaturedAuctionsManager.css';

const FeaturedAuctionsManager = ({ attributes, setAttributes }) => {
    const [availableAuctions, setAvailableAuctions] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // جلب جميع المزادات المتاحة للاختيار
    const fetchAvailableAuctions = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await apiFetch({
                path: '/amlak/v1/auctions?limit=100&status=publish',
            });

            setAvailableAuctions(response || []);
        } catch (err) {
            console.error('Error fetching available auctions:', err);
            setError('حدث خطأ في تحميل المزادات المتاحة');
        } finally {
            setLoading(false);
        }
    };

    // تحميل المزادات عند تفعيل الخاصية
    useEffect(() => {
        if (attributes.enableFeaturedAuctions && availableAuctions.length === 0) {
            fetchAvailableAuctions();
        }
    }, [attributes.enableFeaturedAuctions]);

    // فلترة المزادات حسب البحث
    const filteredAuctions = availableAuctions.filter(auction => {
        if (!searchTerm) return true;
        
        const searchLower = searchTerm.toLowerCase();
        return (
            auction.title.toLowerCase().includes(searchLower) ||
            auction.location.toLowerCase().includes(searchLower) ||
            auction.type.toLowerCase().includes(searchLower) ||
            (auction.agent && auction.agent.toLowerCase().includes(searchLower))
        );
    });

    // الحصول على المزادات المختارة
    const selectedAuctions = availableAuctions.filter(auction => 
        attributes.featuredAuctions.includes(auction.id)
    );

    // إضافة/إزالة مزاد من المميزة
    const toggleAuction = (auctionId, checked) => {
        let newFeaturedAuctions;
        
        if (checked) {
            newFeaturedAuctions = [...attributes.featuredAuctions, auctionId];
        } else {
            newFeaturedAuctions = attributes.featuredAuctions.filter(id => id !== auctionId);
        }
        
        setAttributes({ featuredAuctions: newFeaturedAuctions });
    };

    // إزالة مزاد من المميزة
    const removeFromFeatured = (auctionId) => {
        const newFeaturedAuctions = attributes.featuredAuctions.filter(id => id !== auctionId);
        setAttributes({ featuredAuctions: newFeaturedAuctions });
    };

    // التحقق من كون المزاد مختار
    const isSelected = (auctionId) => {
        return attributes.featuredAuctions.includes(auctionId);
    };

    // تفعيل/إلغاء تفعيل المزادات المميزة
    const handleToggleFeatured = (enabled) => {
        setAttributes({ 
            enableFeaturedAuctions: enabled,
            // إذا تم إلغاء التفعيل، امسح المزادات المختارة
            featuredAuctions: enabled ? attributes.featuredAuctions : []
        });
    };

    return (
        <PanelBody title="🌟 المزادات المميزة" initialOpen={false}>
            <ToggleControl
                label="تفعيل المزادات المميزة"
                help="المزادات المميزة ستظهر في الصف الأول دائماً"
                checked={attributes.enableFeaturedAuctions}
                onChange={handleToggleFeatured}
            />
            
            {attributes.enableFeaturedAuctions && (
                <div className="featured-auctions-manager">
                    
                    {/* شريط البحث */}
                    <div className="search-section">
                        <TextControl
                            label="🔍 البحث في المزادات"
                            placeholder="ابحث بالاسم، المدينة، نوع المزاد، أو الوكيل..."
                            value={searchTerm}
                            onChange={setSearchTerm}
                            help="يمكنك البحث في جميع حقول المزاد"
                        />
                    </div>

                    {/* المزادات المختارة */}
                    {selectedAuctions.length > 0 && (
                        <div className="selected-auctions-section">
                            <h4 className="section-title">
                                المزادات المميزة المختارة ({selectedAuctions.length})
                            </h4>
                            <div className="selected-auctions-tags">
                                {selectedAuctions.map(auction => (
                                    <div key={auction.id} className="selected-auction-tag">
                                        <span className="tag-content">
                                            <strong>{auction.title}</strong>
                                            <small>{auction.location}</small>
                                        </span>
                                        <Button 
                                            isSmall 
                                            isDestructive
                                            className="remove-tag-btn"
                                            onClick={() => removeFromFeatured(auction.id)}
                                            title="إزالة من المميزة"
                                        >
                                            ✕
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* قائمة المزادات المتاحة */}
                    <div className="available-auctions-section">
                        <h4 className="section-title">
                            المزادات المتاحة 
                            {searchTerm && ` (${filteredAuctions.length} من ${availableAuctions.length})`}
                        </h4>

                        {loading && (
                            <div className="loading-container">
                                <Spinner />
                                <span>جاري تحميل المزادات...</span>
                            </div>
                        )}

                        {error && (
                            <div className="error-container">
                                <p className="error-message">{error}</p>
                                <Button 
                                    isSecondary 
                                    isSmall 
                                    onClick={fetchAvailableAuctions}
                                >
                                    إعادة المحاولة
                                </Button>
                            </div>
                        )}

                        {!loading && !error && (
                            <div className="auctions-list">
                                {filteredAuctions.length === 0 ? (
                                    <p className="no-results">
                                        {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد مزادات متاحة'}
                                    </p>
                                ) : (
                                    filteredAuctions.map(auction => (
                                        <AuctionOption
                                            key={auction.id}
                                            auction={auction}
                                            isSelected={isSelected(auction.id)}
                                            onToggle={(checked) => toggleAuction(auction.id, checked)}
                                        />
                                    ))
                                )}
                            </div>
                        )}
                    </div>

                    {/* معلومات إضافية */}
                    {attributes.enableFeaturedAuctions && (
                        <div className="info-section">
                            <p className="help-text">
                                💡 <strong>نصيحة:</strong> المزادات المميزة ستظهر دائماً في الصف الأول 
                                مع تصميم مميز (إطار ذهبي ونجمة).
                            </p>
                        </div>
                    )}
                </div>
            )}
        </PanelBody>
    );
};

// مكون خيار المزاد
const AuctionOption = ({ auction, isSelected, onToggle }) => {
    return (
        <div className={`auction-option ${isSelected ? 'selected' : ''}`}>
            <CheckboxControl
                checked={isSelected}
                onChange={onToggle}
            />
            <div className="auction-info">
                <div className="auction-title">{auction.title}</div>
                <div className="auction-meta">
                    <span className="meta-item">
                        📍 {auction.location}
                    </span>
                    <span className="meta-item">
                        🏷️ {auction.type}
                    </span>
                    <span className="meta-item">
                        🏠 {auction.assetsCount} {auction.assetsCount === 1 ? 'أصل' : 'أصول'}
                    </span>
                    {auction.agent && (
                        <span className="meta-item">
                            👤 {auction.agent}
                        </span>
                    )}
                </div>
            </div>
        </div>
    );
};

export default FeaturedAuctionsManager;
