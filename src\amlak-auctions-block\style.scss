/**
 * Styles for Amlak Auctions Block - Frontend & Editor
 * These styles are applied both in the editor and on the frontend
 */

.wp-block-create-block-amlak-auctions-block {
	direction: rtl;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Grid Layout */
.amlak-auctions-grid {
	display: grid;
	gap: 24px;
	margin: 0;
	padding: 0;
}

/* Loading State */
.amlak-auctions-loading {
	text-align: center;
	padding: 60px 20px;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	border-radius: 16px;
	border: 2px dashed #ddd;
}

.amlak-auctions-loading p {
	color: #666;
	font-size: 16px;
	margin: 16px 0 0 0;
	font-weight: 500;
}

/* Empty State */
.amlak-auctions-empty {
	text-align: center;
	padding: 80px 20px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20px;
	box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
	position: relative;
	overflow: hidden;
}

.amlak-auctions-empty::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
	animation: shimmer 3s ease-in-out infinite;
}

.amlak-auctions-empty p {
	color: #fff;
	font-size: 18px;
	margin: 0;
	font-weight: 600;
	text-shadow: 0 2px 4px rgba(0,0,0,0.3);
	position: relative;
	z-index: 1;
}

.amlak-auctions-empty::after {
	content: '🏠';
	position: absolute;
	top: 20px;
	right: 20px;
	font-size: 40px;
	opacity: 0.3;
}

@keyframes shimmer {
	0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(0deg); }
	50% { transform: translateX(0%) translateY(0%) rotate(180deg); }
}

/* Error State */
.amlak-auctions-error {
	text-align: center;
	padding: 40px 20px;
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	border-radius: 16px;
	color: white;
	box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.amlak-auctions-error p {
	margin: 0 0 16px 0;
	font-size: 16px;
	font-weight: 500;
}

.amlak-auctions-error button {
	background: rgba(255,255,255,0.2);
	color: white;
	border: 2px solid rgba(255,255,255,0.3);
	padding: 12px 24px;
	border-radius: 8px;
	cursor: pointer;
	font-size: 14px;
	font-weight: 500;
	transition: all 0.3s ease;
}

.amlak-auctions-error button:hover {
	background: rgba(255,255,255,0.3);
	border-color: rgba(255,255,255,0.5);
	transform: translateY(-2px);
}

/* ===== AUCTION CARD STYLES ===== */
.amlak-auction-card {
	background: #fff;
	border-radius: 16px;
	box-shadow: 0 4px 24px rgba(0,0,0,0.10);
	padding: 0 0 18px 0;
	display: flex;
	flex-direction: column;
	gap: 10px;
	align-items: stretch;
	min-height: 370px;
	transition: box-shadow 0.2s;
	overflow: hidden;
	border: 1px solid #f0f0f0;
	direction: rtl;
}

.amlak-auction-card:hover {
	box-shadow: 0 8px 32px rgba(0,0,0,0.16);
	border-color: #e0e0e0;
}

/* Clickable Card Styles */
.clickable-card {
	cursor: pointer;
	position: relative;
	transition: all 0.3s ease;
}

.clickable-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 12px 40px rgba(0,0,0,0.2);
	border-color: #BB1919;
}

.clickable-card:active {
	transform: translateY(-2px);
}

/* Click Overlay */
.card-click-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(187, 25, 25, 0.9), rgba(187, 25, 25, 0.7));
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: opacity 0.3s ease;
	border-radius: 16px;
	z-index: 10;
}

.clickable-card:hover .card-click-overlay {
	opacity: 1;
}

.click-indicator {
	display: flex;
	align-items: center;
	gap: 12px;
	color: white;
	font-size: 18px;
	font-weight: 600;
	text-shadow: 0 2px 4px rgba(0,0,0,0.3);
	animation: pulse 2s infinite;
}

.click-indicator svg {
	animation: slideRight 1.5s ease-in-out infinite;
}

@keyframes pulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.05); }
}

@keyframes slideRight {
	0%, 100% { transform: translateX(0); }
	50% { transform: translateX(8px); }
}

/* Focus styles for accessibility */
.clickable-card:focus {
	outline: 3px solid #BB1919;
	outline-offset: 2px;
}

.auction-title-agent-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 14px 18px 8px 18px;
	margin: 0;
	direction: rtl;
}

.auction-title-new {
	font-size: 1.18em;
	font-weight: bold;
	color: #1a2233;
	line-height: 1.3;
	margin: 0;
	max-width: 60%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.auction-agent-new {
	background: #BB1919;
	color: #fff;
	font-size: 0.98em;
	font-weight: 600;
	padding: 2px 12px;
	border-radius: 6px;
	display: inline-block;
	letter-spacing: 0.01em;
	max-width: 35%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.auction-img-wrapper {
	position: relative;
}

.auction-type-badge {
	position: absolute;
	top: 10px;
	right: 10px;
	display: flex;
	align-items: stretch;
	border-radius: 8px;
	z-index: 2;
	box-shadow: none;
	border: none;
	overflow: hidden;
}

.badge-icon-bg {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 12px;
}

.badge-icon-bg-إلكتروني { background: #2196f3; }
.badge-icon-bg-حضوري { background: #43a047; }
.badge-icon-bg-هجين { background: #8e44ad; }

.badge-icon-large {
	width: 28px;
	height: 28px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.badge-icon-large svg {
	width: 22px;
	height: 22px;
	display: block;
	color: #fff;
	fill: #fff;
}

.badge-text-bg {
	background: #f7f7f7;
	color: #222;
	font-size: 1.08em;
	font-weight: 600;
	display: flex;
	align-items: center;
	padding: 0 16px 0 12px;
	letter-spacing: 0.01em;
}

.auction-info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 8px 10px;
	margin: 10px 16px 0 16px;
}

.auction-info-item {
	display: flex;
	align-items: center;
	font-size: 0.97em;
	color: #444;
	background: #f8fafd;
	border-radius: 6px;
	padding: 6px 10px 6px 6px;
	gap: 6px;
	min-width: 0;
	word-break: break-word;
}

.auction-info-item .icon {
	display: flex;
	align-items: center;
	margin-left: 4px;
}

.auction-date-row {
	grid-column: 1 / -1;
	display: flex;
	align-items: center;
	font-size: 0.97em;
	color: #444;
	background: #f8fafd;
	border-radius: 6px;
	padding: 6px 10px 6px 6px;
	gap: 6px;
}

.auction-date-text {
	font-weight: 500;
}

/* ===== IMAGE SLIDER STYLES ===== */
.amlak-img-slider {
	position: relative;
	text-align: center;
	overflow: visible;
	border-radius: 16px 16px 0 0;
	min-height: 180px;
}

.img-slider {
	border-radius: 16px 16px 0 0;
	overflow: hidden;
	min-height: 180px;
}

.img-slider img {
	width: 100%;
	height: 200px;
	object-fit: cover;
	display: block;
}

.slider-img-fade-wrapper {
	position: relative;
	width: 100%;
	min-height: 180px;
	max-height: 200px;
	z-index: 1;
}

.slider-img-fade {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	object-fit: cover;
	opacity: 0;
	transition: opacity 0.7s cubic-bezier(.4,0,.2,1);
	border-radius: 8px;
	z-index: 1;
	pointer-events: none;
}

.slider-img-fade.active {
	opacity: 1;
	z-index: 2;
	pointer-events: auto;
}

.slider-btn {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	background: #fff;
	border: none;
	font-size: 1.5em;
	cursor: pointer;
	z-index: 2;
	padding: 0 10px;
	border-radius: 50%;
	box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.slider-btn.prev { left: 10px; }
.slider-btn.next { right: 10px; }

.slider-dots {
	position: absolute;
	left: 50%;
	bottom: 10px;
	transform: translateX(-50%);
	z-index: 10;
	display: flex;
	gap: 6px;
}

.dot {
	display: inline-block;
	width: 10px;
	height: 10px;
	margin: 0 3px;
	background: #ccc;
	border-radius: 50%;
	cursor: pointer;
}

.dot.active {
	background: #007cba;
}

/* ===== COUNTDOWN STYLES ===== */
.countdown-container {
	margin: 8px 16px 0 16px;
}

.countdown-loading {
	text-align: center;
	color: #666;
	font-size: 14px;
	padding: 10px;
}

.countdown-expired {
	text-align: center;
	color: #c33;
	font-weight: bold;
	padding: 10px;
	background: #fee;
	border-radius: 8px;
	border: 1px solid #fcc;
}

.countdown-timer {
	display: flex;
	align-items: center;
	gap: 10px;
	justify-content: center;
	margin: 14px 0 0 0;
}

.countdown-item {
	background: rgba(30,30,30,0.08);
	border-radius: 8px;
	min-width: 48px;
	min-height: 48px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 1px 4px rgba(0,0,0,0.08);
	border: 1.5px solid #e0e0e0;
	padding: 6px 10px 4px 10px;
}

.countdown-number {
	font-size: 1.25em;
	font-weight: bold;
	color: #007cba;
	margin-bottom: 2px;
}

.countdown-label {
	font-size: 0.85em;
	color: #666;
	font-weight: 500;
}
