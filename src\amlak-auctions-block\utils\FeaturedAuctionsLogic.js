/**
 * Featured Auctions Logic
 * منطق إدارة وترتيب المزادات المميزة
 */

/**
 * فصل المزادات إلى مميزة وعادية
 * @param {Array} allAuctions - جميع المزادات
 * @param {Array} featuredIds - معرفات المزادات المميزة
 * @param {boolean} enableFeatured - هل المزادات المميزة مفعلة
 * @returns {Object} - كائن يحتوي على المزادات المميزة والعادية
 */
export const separateAuctions = (allAuctions, featuredIds = [], enableFeatured = false) => {
    if (!enableFeatured || !featuredIds.length) {
        return {
            featured: [],
            regular: allAuctions,
            combined: allAuctions
        };
    }

    const featured = [];
    const regular = [];
    
    // إنشاء خريطة للمزادات المميزة للبحث السريع
    const featuredIdsSet = new Set(featuredIds);
    
    allAuctions.forEach(auction => {
        if (featuredIdsSet.has(auction.id)) {
            featured.push({
                ...auction,
                isFeatured: true
            });
        } else {
            regular.push({
                ...auction,
                isFeatured: false
            });
        }
    });

    // ترتيب المزادات المميزة حسب ترتيب الاختيار
    const sortedFeatured = featuredIds
        .map(id => featured.find(auction => auction.id === id))
        .filter(Boolean); // إزالة القيم الفارغة

    return {
        featured: sortedFeatured,
        regular,
        combined: [...sortedFeatured, ...regular]
    };
};

/**
 * ترتيب المزادات في شبكة مع إعطاء الأولوية للمميزة
 * @param {Array} auctions - المزادات المرتبة
 * @param {number} columns - عدد الأعمدة
 * @param {boolean} enableFeatured - هل المزادات المميزة مفعلة
 * @returns {Array} - المزادات مرتبة في صفوف
 */
export const arrangeAuctionsInGrid = (auctions, columns = 3, enableFeatured = false) => {
    if (!auctions.length) return [];

    const rows = [];
    let currentRow = [];
    
    auctions.forEach((auction, index) => {
        currentRow.push(auction);
        
        // إذا امتلأ الصف أو انتهت المزادات
        if (currentRow.length === columns || index === auctions.length - 1) {
            rows.push({
                auctions: [...currentRow],
                isFeaturedRow: enableFeatured && currentRow.some(a => a.isFeatured),
                rowIndex: rows.length
            });
            currentRow = [];
        }
    });

    return rows;
};

/**
 * حساب إحصائيات المزادات المميزة
 * @param {Array} allAuctions - جميع المزادات
 * @param {Array} featuredIds - معرفات المزادات المميزة
 * @returns {Object} - إحصائيات المزادات
 */
export const getFeaturedAuctionsStats = (allAuctions, featuredIds = []) => {
    const totalAuctions = allAuctions.length;
    const featuredCount = featuredIds.length;
    const regularCount = totalAuctions - featuredCount;
    
    return {
        total: totalAuctions,
        featured: featuredCount,
        regular: regularCount,
        featuredPercentage: totalAuctions > 0 ? Math.round((featuredCount / totalAuctions) * 100) : 0
    };
};

/**
 * التحقق من صحة معرفات المزادات المميزة
 * @param {Array} featuredIds - معرفات المزادات المميزة
 * @param {Array} availableAuctions - المزادات المتاحة
 * @returns {Object} - نتيجة التحقق
 */
export const validateFeaturedAuctions = (featuredIds = [], availableAuctions = []) => {
    const availableIds = new Set(availableAuctions.map(auction => auction.id));
    const validIds = featuredIds.filter(id => availableIds.has(id));
    const invalidIds = featuredIds.filter(id => !availableIds.has(id));
    
    return {
        valid: validIds,
        invalid: invalidIds,
        hasInvalid: invalidIds.length > 0,
        allValid: invalidIds.length === 0
    };
};

/**
 * تنظيف معرفات المزادات المميزة من المعرفات غير الصحيحة
 * @param {Array} featuredIds - معرفات المزادات المميزة
 * @param {Array} availableAuctions - المزادات المتاحة
 * @returns {Array} - معرفات صحيحة فقط
 */
export const cleanupFeaturedAuctions = (featuredIds = [], availableAuctions = []) => {
    const validation = validateFeaturedAuctions(featuredIds, availableAuctions);
    return validation.valid;
};

/**
 * إنشاء CSS classes للمزادات حسب نوعها
 * @param {Object} auction - بيانات المزاد
 * @param {number} index - فهرس المزاد
 * @param {boolean} isFirstRow - هل هو في الصف الأول
 * @returns {string} - CSS classes
 */
export const getAuctionCardClasses = (auction, index = 0, isFirstRow = false) => {
    const baseClass = 'amlak-auction-card';
    const classes = [baseClass];
    
    if (auction.isFeatured) {
        classes.push('featured-auction');
        classes.push('featured-auction-card');
    }
    
    if (isFirstRow) {
        classes.push('first-row-auction');
    }
    
    if (auction.isFeatured && isFirstRow) {
        classes.push('featured-first-row');
    }
    
    // إضافة class للموضع
    classes.push(`auction-position-${index}`);
    
    return classes.join(' ');
};

/**
 * إنشاء inline styles للمزادات المميزة
 * @param {Object} auction - بيانات المزاد
 * @param {Object} options - خيارات التصميم
 * @returns {Object} - inline styles
 */
export const getFeaturedAuctionStyles = (auction, options = {}) => {
    if (!auction.isFeatured) {
        return {};
    }
    
    const {
        borderColor = '#ffd700',
        borderWidth = '2px',
        boxShadow = '0 4px 12px rgba(255, 215, 0, 0.3)',
        transform = 'translateY(-2px)'
    } = options;
    
    return {
        border: `${borderWidth} solid ${borderColor}`,
        boxShadow,
        transform,
        position: 'relative',
        zIndex: 1
    };
};

/**
 * إنشاء محتوى شارة المزاد المميز
 * @param {Object} auction - بيانات المزاد
 * @param {Object} options - خيارات الشارة
 * @returns {string} - HTML للشارة
 */
export const getFeaturedBadgeHTML = (auction, options = {}) => {
    if (!auction.isFeatured) {
        return '';
    }
    
    const {
        text = 'مميز',
        icon = '⭐',
        position = 'top-right',
        backgroundColor = '#ffd700',
        textColor = '#1a1a1a'
    } = options;
    
    return `
        <div class="featured-badge featured-badge-${position}" 
             style="background-color: ${backgroundColor}; color: ${textColor};">
            <span class="featured-icon">${icon}</span>
            <span class="featured-text">${text}</span>
        </div>
    `;
};

/**
 * تحديث ترتيب المزادات المميزة
 * @param {Array} currentFeaturedIds - المعرفات الحالية
 * @param {number} fromIndex - الفهرس الأصلي
 * @param {number} toIndex - الفهرس الجديد
 * @returns {Array} - المعرفات مرتبة جديداً
 */
export const reorderFeaturedAuctions = (currentFeaturedIds, fromIndex, toIndex) => {
    const newOrder = [...currentFeaturedIds];
    const [movedItem] = newOrder.splice(fromIndex, 1);
    newOrder.splice(toIndex, 0, movedItem);
    return newOrder;
};

/**
 * إنشاء معاينة للمزادات المميزة
 * @param {Array} featuredAuctions - المزادات المميزة
 * @param {number} maxPreview - أقصى عدد للمعاينة
 * @returns {Object} - بيانات المعاينة
 */
export const createFeaturedPreview = (featuredAuctions, maxPreview = 3) => {
    const preview = featuredAuctions.slice(0, maxPreview);
    const remaining = Math.max(0, featuredAuctions.length - maxPreview);
    
    return {
        preview,
        remaining,
        hasMore: remaining > 0,
        total: featuredAuctions.length
    };
};

/**
 * تصدير جميع الدوال كـ default object
 */
export default {
    separateAuctions,
    arrangeAuctionsInGrid,
    getFeaturedAuctionsStats,
    validateFeaturedAuctions,
    cleanupFeaturedAuctions,
    getAuctionCardClasses,
    getFeaturedAuctionStyles,
    getFeaturedBadgeHTML,
    reorderFeaturedAuctions,
    createFeaturedPreview
};
