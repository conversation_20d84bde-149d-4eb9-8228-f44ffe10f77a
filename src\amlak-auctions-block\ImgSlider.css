.amlak-img-slider {
  position: relative;
  text-align: center;
  overflow: visible;
}
.slider-img-fade-wrapper {
  position: relative;
  width: 100%;
  min-height: 180px;
  max-height: 200px;
  z-index: 1;
}
.slider-img-fade {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.7s cubic-bezier(.4,0,.2,1);
  border-radius: 8px;
  z-index: 1;
  pointer-events: none;
}
.slider-img-fade.active {
  opacity: 1;
  z-index: 2;
  pointer-events: auto;
}
.slider-dots {
  position: absolute;
  left: 50%;
  bottom: 10px;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  gap: 6px;
}
.slider-img-fade-wrapper {
  position: relative;
  width: 100%;
  min-height: 180px;
  max-height: 200px;
}
.slider-img-fade {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.7s cubic-bezier(.4,0,.2,1);
  border-radius: 8px;
  z-index: 1;
  pointer-events: none;
}
.slider-img-fade.active {
  opacity: 1;
  z-index: 2;
  pointer-events: auto;
}
.slider-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: #fff;
  border: none;
  font-size: 1.5em;
  cursor: pointer;
  z-index: 2;
  padding: 0 10px;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}
.slider-btn.prev { left: 10px; }
.slider-btn.next { right: 10px; }
.slider-dots {
  margin-top: 8px;
}
.dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 0 3px;
  background: #ccc;
  border-radius: 50%;
  cursor: pointer;
}
.dot.active {
  background: #007cba;
}
