/* Featured Auctions Styles */

/* Container for auctions with featured info */
.amlak-auctions-container {
    position: relative;
}

/* تم إزالة CSS الخاص برسالة المزادات المميزة */

/* Featured auction card styles - توحيد التصميم */
.featured-auction-card {
    position: relative;
    overflow: visible;
}

/* إزالة الإطار في الباك إند فقط */
.wp-block-create-block-amlak-auctions-block .featured-auction-card {
    border: none !important;
    box-shadow: none !important;
    transform: none !important;
    transition: none !important;
    background: transparent !important;
}

.wp-block-create-block-amlak-auctions-block .featured-auction-card:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* تطبيق التصميم المميز في الواجهة الأمامية */
.amlak-auctions-grid .featured-auction-card {
    border: 2px solid #ff6b35 !important;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3) !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff, #fffaf7);
}

.amlak-auctions-grid .featured-auction-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(255, 107, 53, 0.4) !important;
}

/* التأكد من أن الشارة تظهر فوق جميع العناصر */
.featured-auction-card .auction-img-wrapper {
    position: relative;
    overflow: visible;
}

/* إصلاح ارتفاع الكارت في الباك إند */
.wp-block-create-block-amlak-auctions-block .amlak-auction-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

.wp-block-create-block-amlak-auctions-block .amlak-auction-card > * {
    height: auto !important;
}

/* Featured badge - شارة شريط مائل أنيقة موحدة */
.featured-badge {
    position: absolute;
    z-index: 10;
    top: 15px;
    left: -10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 110px;
    height: 30px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transform: rotate(-45deg);
    box-shadow:
        0 3px 10px rgba(255, 107, 53, 0.4),
        0 1px 0 rgba(255, 255, 255, 0.2) inset;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.featured-badge:hover {
    background: linear-gradient(135deg, #f7931e, #ff6b35);
    box-shadow:
        0 4px 12px rgba(255, 107, 53, 0.5),
        0 1px 0 rgba(255, 255, 255, 0.3) inset;
    transform: rotate(-45deg) scale(1.05);
}

/* إضافة تأثير الشريط */
.featured-badge::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #ff8c42, #ff6b35);
    transform: rotate(0deg);
    z-index: -1;
    border-radius: 2px;
}

.featured-badge::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -12px;
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-right: 10px solid #d63031;
    transform: translateY(-50%);
}

/* أيقونة ونص الشارة المائلة */
.featured-icon {
    font-size: 12px;
    line-height: 1;
    margin-left: 4px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.featured-text {
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    line-height: 1;
}

/* تأثير لمعان للشارة المائلة */
@keyframes ribbonShine {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

.featured-badge {
    background-image:
        linear-gradient(135deg, #ff6b35, #f7931e),
        linear-gradient(135deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
    background-size: 100% 100%, 200% 100%;
    animation: ribbonShine 3s ease-in-out infinite;
}

/* شارة بديلة - تصميم الماس */
.featured-badge.diamond-style {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 8px;
    transform: rotate(45deg);
    width: 40px;
    height: 40px;
    justify-content: center;
    padding: 0;
}

.featured-badge.diamond-style .featured-content {
    transform: rotate(-45deg);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.featured-badge.diamond-style .featured-icon {
    font-size: 12px;
}

.featured-badge.diamond-style .featured-text {
    font-size: 8px;
    line-height: 1;
}

/* شارة بديلة - تصميم النجمة */
.featured-badge.star-style {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a1a;
    border: 2px solid #f59e0b;
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    width: 45px;
    height: 45px;
    padding: 0;
    justify-content: center;
    transform: rotate(0deg);
}

.featured-badge.star-style .featured-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1px;
    margin-top: 8px;
}

.featured-badge.star-style .featured-icon {
    font-size: 10px;
}

.featured-badge.star-style .featured-text {
    font-size: 7px;
    line-height: 1;
}

/* تأثيرات الحركة للشارة المائلة */
.featured-badge {
    animation: ribbonFloat 4s ease-in-out infinite;
}

@keyframes ribbonFloat {
    0%, 100% {
        transform: rotate(-45deg) translateY(0px);
    }
    50% {
        transform: rotate(-45deg) translateY(-2px);
    }
}

/* Featured auction in first row */
.featured-first-row {
    background: linear-gradient(135deg, #ffffff, #fffbf0);
}

/* Animation for featured auctions */
@keyframes featuredGlow {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    }
    50% {
        box-shadow: 0 4px 16px rgba(255, 215, 0, 0.5);
    }
}

.featured-auction-card {
    animation: featuredGlow 3s ease-in-out infinite;
}

/* Featured auction type badge enhancement */
.featured-auction-card .auction-type-badge {
    border: 1px solid #ffd700;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.2);
}

/* Featured auction title enhancement */
.featured-auction-card .auction-title-new {
    color: #1a1a1a;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(255, 215, 0, 0.1);
}

/* Featured auction info items enhancement */
.featured-auction-card .auction-info-item {
    background: rgba(255, 215, 0, 0.05);
    border-radius: 6px;
    padding: 8px;
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.featured-auction-card .auction-info-item .icon svg {
    filter: drop-shadow(0 1px 2px rgba(255, 215, 0, 0.3));
}

/* Featured auction countdown enhancement */
.featured-auction-card .countdown-timer {
    background: linear-gradient(135deg, #fff9e6, #fef3c7);
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 12px;
}

.featured-auction-card .countdown-item {
    background: #ffffff;
    border: 1px solid #ffd700;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.1);
}

.featured-auction-card .countdown-number {
    color: #92400e;
    font-weight: 700;
}

.featured-auction-card .countdown-label {
    color: #a16207;
}

/* Featured auction click overlay enhancement */
.featured-auction-card .card-click-overlay {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 237, 78, 0.9));
}

.featured-auction-card .click-indicator {
    color: #1a1a1a;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

/* Featured placeholder in editor */
.featured-placeholder {
    color: #92400e;
    font-size: 12px;
    font-style: italic;
    margin: 4px 0 0 0;
}

/* Responsive design for featured auctions */
@media (max-width: 768px) {
    .featured-badge {
        width: 90px;
        height: 26px;
        top: 12px;
        left: -8px;
        font-size: 11px;
    }

    .featured-icon {
        font-size: 11px;
        margin-left: 3px;
    }

    .featured-text {
        font-size: 9px;
        letter-spacing: 0.4px;
    }

    .featured-badge::after {
        border-top: 13px solid transparent;
        border-bottom: 13px solid transparent;
        border-right: 8px solid #d63031;
        left: -10px;
    }
}

@media (max-width: 480px) {
    .featured-badge {
        width: 80px;
        height: 24px;
        top: 10px;
        left: -7px;
        font-size: 10px;
    }

    .featured-icon {
        font-size: 10px;
        margin-left: 2px;
    }

    .featured-text {
        font-size: 8px;
    }

    .featured-badge::after {
        border-top: 12px solid transparent;
        border-bottom: 12px solid transparent;
        border-right: 7px solid #d63031;
        left: -9px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .featured-auction-card {
        border-color: #000000 !important;
        background: #ffffff !important;
    }
    
    .featured-badge {
        background: #000000 !important;
        color: #ffffff !important;
        border-color: #000000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .featured-auction-card {
        animation: none;
        transform: none;
    }
    
    .featured-auction-card:hover {
        transform: none;
    }
}

/* Print styles */
@media print {
    .featured-auction-card {
        border: 2px solid #000000 !important;
        box-shadow: none !important;
        transform: none !important;
        animation: none !important;
    }
    
    .featured-badge {
        background: #000000 !important;
        color: #ffffff !important;
    }
    
    .featured-auctions-info {
        background: #f5f5f5 !important;
        border: 1px solid #000000 !important;
    }
}

/* Focus styles for accessibility */
.featured-auction-card:focus {
    outline: 3px solid #ffd700;
    outline-offset: 2px;
}

.featured-auction-card:focus-visible {
    outline: 3px solid #ffd700;
    outline-offset: 2px;
}

/* Loading state for featured auctions */
.featured-auction-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.featured-auction-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}
