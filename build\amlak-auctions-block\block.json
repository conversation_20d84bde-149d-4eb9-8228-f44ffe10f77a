{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "create-block/amlak-auctions-block", "version": "0.1.0", "title": "Amlak Auctions Block", "category": "widgets", "icon": "smiley", "description": "Block to display auctions as cards with full admin control.", "example": {}, "supports": {"html": false}, "attributes": {"columns": {"type": "number", "default": 3}, "auctionsCount": {"type": "number", "default": 6}, "showImages": {"type": "boolean", "default": true}, "showTitle": {"type": "boolean", "default": true}, "showType": {"type": "boolean", "default": true}, "showLocation": {"type": "boolean", "default": true}, "showAssetsCount": {"type": "boolean", "default": true}, "showDateTime": {"type": "boolean", "default": true}, "showCountdown": {"type": "boolean", "default": true}, "showAgent": {"type": "boolean", "default": true}, "featuredAuctions": {"type": "array", "default": []}}, "textdomain": "amlak-auctions-block", "editorScript": "file:./index.js", "editorStyle": "file:./index.css", "style": "file:./style-index.css", "viewScript": "file:./view.js"}