import React from 'react';
import { FeaturedBadge, BADGE_STYLES } from './FeaturedBadgeStyles';

// دالة لتحويل التاريخ إلى نص عربي جميل
const formatAuctionDateTime = (date, time) => {
    if (!date) return '';
    // إذا كان النص طويل جداً اختصر اسم الشهر
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    const monthsShort = [
        'ينا', 'فبر', 'مار', 'أبر', 'ماي', 'يون',
        'يول', 'أغس', 'سبت', 'أكت', 'نوف', 'ديس'
    ];
    const [year, month, day] = date.split('-');
    let monthName = months[parseInt(month, 10) - 1] || '';
    let result = `${parseInt(day, 10)} ${monthName} ${year}${time ? ' - ' + time : ''}`;
    // إذا كان النص طويل جداً اختصر الشهر
    if (result.length > 22) {
        monthName = monthsShort[parseInt(month, 10) - 1] || '';
        result = `${parseInt(day, 10)} ${monthName} ${year}${time ? ' - ' + time : ''}`;
    }
    return result;
};
import ImgSlider from './ImgSlider';
import Countdown from './Countdown';
import './Card.css';

const Card = ({ auction, settings, isFeatured = false }) => {
    // أيقونة نوع المزاد حسب النوع
    // تصميم أيقونة واضحة جدًا لكل نوع
    const auctionTypeIcon = {
        'إلكتروني': (
            <svg width="22" height="22" viewBox="0 0 24 24" fill="white"><rect x="3" y="6" width="18" height="9" rx="2"/><rect x="7" y="16" width="10" height="2" rx="1"/></svg>
        ),
        'حضوري': (
            <svg width="22" height="22" viewBox="0 0 24 24" fill="white"><path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05C15.64 13.36 17 14.28 17 15.5V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/></svg>
        ),
        'هجين': (
            <span style={{display:'flex',alignItems:'center',gap:'1px'}}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="white"><rect x="3" y="6" width="18" height="9" rx="2"/><rect x="7" y="16" width="10" height="2" rx="1"/></svg>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="white"><path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05C15.64 13.36 17 14.28 17 15.5V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/></svg>
            </span>
        ),
    };

    // Handle card click - only in editor, frontend will use actual links
    const handleCardClick = () => {
        if (auction.url) {
            // In editor, show a message instead of navigating
            alert(`سيتم الانتقال إلى صفحة المزاد: ${auction.title}`);
        }
    };

    // إنشاء CSS classes للكارت
    const cardClasses = ['amlak-auction-card', 'clickable-card'];
    if (isFeatured) {
        cardClasses.push('featured-auction', 'featured-auction-card');
    }

    return (
        <div
            className={cardClasses.join(' ')}
            onClick={handleCardClick}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    handleCardClick();
                }
            }}
        >
            {/* شارة المزاد المميز - تصميم أنيق طولي */}
            {isFeatured && (
                <div className="featured-badge">
                    <span className="featured-icon">⭐</span>
                    <span className="featured-text">مميز</span>
                </div>
            )}
            {settings.showImages && auction.images && (
                <div className="auction-img-wrapper">
                    {settings.showType && (
                        <span className={`auction-type-badge auction-type-${auction.type}`}>
                            <span className={`badge-icon-bg badge-icon-bg-${auction.type}`}>
                                <span className="badge-icon-large">{auctionTypeIcon[auction.type]}</span>
                            </span>
                            <span className="badge-text-bg">{auction.type}</span>
                        </span>
                    )}
                    <ImgSlider images={auction.images} />
                </div>
            )}
            {settings.showTitle && (
                <div className="auction-title-agent-row">
                    <h2 className="auction-title-new">{auction.title}</h2>
                    {settings.showAgent && (
                        <div className="auction-agent-new">{auction.agent}</div>
                    )}
                </div>
            )}
            <div className="auction-info-grid">
                {settings.showLocation && (
                    <div className="auction-info-item">
                        <span className="icon">
                            <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 11.5A2.5 2.5 0 1112 8a2.5 2.5 0 010 5.5z"/></svg>
                        </span>
                        {auction.location}
                    </div>
                )}
                {settings.showAssetsCount && (
                    <div className="auction-info-item">
                        <span className="icon">
                            {/* أيقونة مبنى عالي */}
                            <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M4 22V2h16v20h-7v-6h-2v6H4zm2-2h4v-6h4v6h4V4H6v16z"/></svg>
                        </span>
                        {auction.assetsCount} {auction.assetsCount === 1 ? 'أصل' : 'أصول'}
                    </div>
                )}
                {settings.showDateTime && (
                    <div className="auction-date-row">
                        <span className="icon">
                            {/* أيقونة تقويم */}
                            <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11zm0-13H5V6h14v1zm-7 5h5v5h-5z"/></svg>
                        </span>
                        <span className="auction-date-text">{formatAuctionDateTime(auction.date, auction.time)}</span>
                    </div>
                )}
            </div>
            {settings.showCountdown && auction.endDateTime && (
                <Countdown endDateTime={auction.endDateTime} />
            )}

            {/* Click indicator overlay */}
            <div className="card-click-overlay">
                <div className="click-indicator">
                    <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                        <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                    </svg>
                    <span>اضغط للعرض</span>
                </div>
            </div>
        </div>
    );
};

export default Card;
