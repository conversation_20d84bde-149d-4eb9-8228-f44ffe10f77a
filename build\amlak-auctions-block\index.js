(()=>{"use strict";var e,s={307:(e,s,t)=>{const a=window.wp.blocks,n=window.React,i=window.wp.blockEditor,l=window.wp.apiFetch;var c=t.n(l);const o=window.wp.components,r=window.ReactJSXRuntime,d=({images:e=[]})=>{const[s,t]=(0,n.useState)(0),[a,i]=(0,n.useState)(!1),l=(0,n.useRef)();return e.length?((0,n.useEffect)((()=>{if(!a)return l.current=setInterval((()=>{t((s=>(s+1)%e.length))}),3e3),()=>clearInterval(l.current)}),[e.length,a]),(0,r.jsxs)("div",{className:"amlak-img-slider",onMouseEnter:()=>i(!0),onMouseLeave:()=>i(!1),children:[(0,r.jsx)("div",{className:"slider-img-fade-wrapper",children:e.map(((e,t)=>(0,r.jsx)("img",{src:e,alt:"صورة المزاد",className:"slider-img-fade"+(t===s?" active":""),style:{zIndex:t===s?2:1}},t)))}),(0,r.jsx)("div",{className:"slider-dots",children:e.map(((e,a)=>(0,r.jsx)("span",{className:a===s?"dot active":"dot",onClick:()=>t(a)},a)))})]})):null},h=e=>{const s=Date.parse(e)-Date.now(),t=Math.floor(s/1e3%60),a=Math.floor(s/1e3/60%60),n=Math.floor(s/36e5%24);return{total:s,days:Math.floor(s/864e5),hours:n,minutes:a,seconds:t}},u=({endDateTime:e})=>{const[s,t]=(0,n.useState)(h(e));return(0,n.useEffect)((()=>{const s=setInterval((()=>{t(h(e))}),1e3);return()=>clearInterval(s)}),[e]),s.total<=0?(0,r.jsx)("div",{className:"amlak-countdown",children:"انتهى المزاد"}):(0,r.jsx)("div",{className:"amlak-countdown-cards-row",children:(0,r.jsxs)("div",{className:"countdown-rects",children:[(0,r.jsxs)("div",{className:"countdown-rect",children:[(0,r.jsx)("div",{className:"countdown-num",children:s.days}),(0,r.jsx)("div",{className:"countdown-label",children:"يوم"})]}),(0,r.jsxs)("div",{className:"countdown-rect",children:[(0,r.jsx)("div",{className:"countdown-num",children:s.hours}),(0,r.jsx)("div",{className:"countdown-label",children:"ساعة"})]}),(0,r.jsxs)("div",{className:"countdown-rect",children:[(0,r.jsx)("div",{className:"countdown-num",children:s.minutes}),(0,r.jsx)("div",{className:"countdown-label",children:"دقيقة"})]}),(0,r.jsxs)("div",{className:"countdown-rect",children:[(0,r.jsx)("div",{className:"countdown-num",children:s.seconds}),(0,r.jsx)("div",{className:"countdown-label",children:"ثانية"})]})]})})},m=(e,s)=>{if(!e)return"";const[t,a,n]=e.split("-");let i=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"][parseInt(a,10)-1]||"",l=`${parseInt(n,10)} ${i} ${t}${s?" - "+s:""}`;return l.length>22&&(i=["ينا","فبر","مار","أبر","ماي","يون","يول","أغس","سبت","أكت","نوف","ديس"][parseInt(a,10)-1]||"",l=`${parseInt(n,10)} ${i} ${t}${s?" - "+s:""}`),l},x=({auction:e,settings:s,isFeatured:t=!1})=>{const a={إلكتروني:(0,r.jsxs)("svg",{width:"22",height:"22",viewBox:"0 0 24 24",fill:"white",children:[(0,r.jsx)("rect",{x:"3",y:"6",width:"18",height:"9",rx:"2"}),(0,r.jsx)("rect",{x:"7",y:"16",width:"10",height:"2",rx:"1"})]}),حضوري:(0,r.jsx)("svg",{width:"22",height:"22",viewBox:"0 0 24 24",fill:"white",children:(0,r.jsx)("path",{d:"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05C15.64 13.36 17 14.28 17 15.5V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"})}),هجين:(0,r.jsxs)("span",{style:{display:"flex",alignItems:"center",gap:"1px"},children:[(0,r.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"white",children:[(0,r.jsx)("rect",{x:"3",y:"6",width:"18",height:"9",rx:"2"}),(0,r.jsx)("rect",{x:"7",y:"16",width:"10",height:"2",rx:"1"})]}),(0,r.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"white",children:(0,r.jsx)("path",{d:"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05C15.64 13.36 17 14.28 17 15.5V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"})})]})},n=()=>{e.url&&alert(`سيتم الانتقال إلى صفحة المزاد: ${e.title}`)},i=["amlak-auction-card","clickable-card"];return t&&i.push("featured-auction","featured-auction-card"),(0,r.jsxs)("div",{className:i.join(" "),onClick:n,role:"button",tabIndex:0,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||n()},children:[t&&(0,r.jsxs)("div",{className:"featured-badge",children:[(0,r.jsx)("span",{className:"featured-icon",children:"⭐"}),(0,r.jsx)("span",{className:"featured-text",children:"مميز"})]}),s.showImages&&e.images&&(0,r.jsxs)("div",{className:"auction-img-wrapper",children:[s.showType&&(0,r.jsxs)("span",{className:`auction-type-badge auction-type-${e.type}`,children:[(0,r.jsx)("span",{className:`badge-icon-bg badge-icon-bg-${e.type}`,children:(0,r.jsx)("span",{className:"badge-icon-large",children:a[e.type]})}),(0,r.jsx)("span",{className:"badge-text-bg",children:e.type})]}),(0,r.jsx)(d,{images:e.images})]}),s.showTitle&&(0,r.jsxs)("div",{className:"auction-title-agent-row",children:[(0,r.jsx)("h2",{className:"auction-title-new",children:e.title}),s.showAgent&&(0,r.jsx)("div",{className:"auction-agent-new",children:e.agent})]}),(0,r.jsxs)("div",{className:"auction-info-grid",children:[s.showLocation&&(0,r.jsxs)("div",{className:"auction-info-item",children:[(0,r.jsx)("span",{className:"icon",children:(0,r.jsx)("svg",{width:"24",height:"24",fill:"#BB1919",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 11.5A2.5 2.5 0 1112 8a2.5 2.5 0 010 5.5z"})})}),e.location]}),s.showAssetsCount&&(0,r.jsxs)("div",{className:"auction-info-item",children:[(0,r.jsx)("span",{className:"icon",children:(0,r.jsx)("svg",{width:"24",height:"24",fill:"#BB1919",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M4 22V2h16v20h-7v-6h-2v6H4zm2-2h4v-6h4v6h4V4H6v16z"})})}),e.assetsCount," ",1===e.assetsCount?"أصل":"أصول"]}),s.showDateTime&&(0,r.jsxs)("div",{className:"auction-date-row",children:[(0,r.jsx)("span",{className:"icon",children:(0,r.jsx)("svg",{width:"24",height:"24",fill:"#BB1919",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M19 4h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11zm0-13H5V6h14v1zm-7 5h5v5h-5z"})})}),(0,r.jsx)("span",{className:"auction-date-text",children:m(e.date,e.time)})]})]}),s.showCountdown&&e.endDateTime&&(0,r.jsx)(u,{endDateTime:e.endDateTime}),(0,r.jsx)("div",{className:"card-click-overlay",children:(0,r.jsxs)("div",{className:"click-indicator",children:[(0,r.jsx)("svg",{width:"24",height:"24",fill:"white",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"})}),(0,r.jsx)("span",{children:"اضغط للعرض"})]})})]})},g=({auction:e,isSelected:s,onToggle:t})=>(0,r.jsxs)("div",{className:"auction-option "+(s?"selected":""),children:[(0,r.jsx)(o.CheckboxControl,{checked:s,onChange:t}),(0,r.jsxs)("div",{className:"auction-info",children:[(0,r.jsx)("div",{className:"auction-title",children:e.title}),(0,r.jsxs)("div",{className:"auction-meta",children:[(0,r.jsxs)("span",{className:"meta-item",children:["📍 ",e.location]}),(0,r.jsxs)("span",{className:"meta-item",children:["🏷️ ",e.type]}),(0,r.jsxs)("span",{className:"meta-item",children:["🏠 ",e.assetsCount," ",1===e.assetsCount?"أصل":"أصول"]}),e.agent&&(0,r.jsxs)("span",{className:"meta-item",children:["👤 ",e.agent]})]})]})]}),j=({attributes:e,setAttributes:s})=>{const[t,a]=(0,n.useState)([]),[i,l]=(0,n.useState)(""),[d,h]=(0,n.useState)(!1),[u,m]=(0,n.useState)(null),x=async()=>{try{h(!0),m(null);const e=await c()({path:"/amlak/v1/auctions?limit=100&status=publish"});a(e||[])}catch(e){console.error("Error fetching available auctions:",e),m("حدث خطأ في تحميل المزادات المتاحة")}finally{h(!1)}};(0,n.useEffect)((()=>{e.enableFeaturedAuctions&&0===t.length&&x()}),[e.enableFeaturedAuctions]);const j=t.filter((e=>{if(!i)return!0;const s=i.toLowerCase();return e.title.toLowerCase().includes(s)||e.location.toLowerCase().includes(s)||e.type.toLowerCase().includes(s)||e.agent&&e.agent.toLowerCase().includes(s)})),p=t.filter((s=>e.featuredAuctions.includes(s.id)));return(0,r.jsxs)(o.PanelBody,{title:"🌟 المزادات المميزة",initialOpen:!1,children:[(0,r.jsx)(o.ToggleControl,{label:"تفعيل المزادات المميزة",help:"المزادات المميزة ستظهر في الصف الأول دائماً",checked:e.enableFeaturedAuctions,onChange:t=>{s({enableFeaturedAuctions:t,featuredAuctions:t?e.featuredAuctions:[]})}}),e.enableFeaturedAuctions&&(0,r.jsxs)("div",{className:"featured-auctions-manager",children:[(0,r.jsx)("div",{className:"search-section",children:(0,r.jsx)(o.TextControl,{label:"🔍 البحث في المزادات",placeholder:"ابحث بالاسم، المدينة، نوع المزاد، أو الوكيل...",value:i,onChange:l,help:"يمكنك البحث في جميع حقول المزاد"})}),p.length>0&&(0,r.jsxs)("div",{className:"selected-auctions-section",children:[(0,r.jsxs)("h4",{className:"section-title",children:["المزادات المميزة المختارة (",p.length,")"]}),(0,r.jsx)("div",{className:"selected-auctions-tags",children:p.map((t=>(0,r.jsxs)("div",{className:"selected-auction-tag",children:[(0,r.jsxs)("span",{className:"tag-content",children:[(0,r.jsx)("strong",{children:t.title}),(0,r.jsx)("small",{children:t.location})]}),(0,r.jsx)(o.Button,{isSmall:!0,isDestructive:!0,className:"remove-tag-btn",onClick:()=>(t=>{const a=e.featuredAuctions.filter((e=>e!==t));s({featuredAuctions:a})})(t.id),title:"إزالة من المميزة",children:"✕"})]},t.id)))})]}),(0,r.jsxs)("div",{className:"available-auctions-section",children:[(0,r.jsxs)("h4",{className:"section-title",children:["المزادات المتاحة",i&&` (${j.length} من ${t.length})`]}),d&&(0,r.jsxs)("div",{className:"loading-container",children:[(0,r.jsx)(o.Spinner,{}),(0,r.jsx)("span",{children:"جاري تحميل المزادات..."})]}),u&&(0,r.jsxs)("div",{className:"error-container",children:[(0,r.jsx)("p",{className:"error-message",children:u}),(0,r.jsx)(o.Button,{isSecondary:!0,isSmall:!0,onClick:x,children:"إعادة المحاولة"})]}),!d&&!u&&(0,r.jsx)("div",{className:"auctions-list",children:0===j.length?(0,r.jsx)("p",{className:"no-results",children:i?"لا توجد نتائج للبحث":"لا توجد مزادات متاحة"}):j.map((t=>{return(0,r.jsx)(g,{auction:t,isSelected:(a=t.id,e.featuredAuctions.includes(a)),onToggle:a=>((t,a)=>{let n;n=a?[...e.featuredAuctions,t]:e.featuredAuctions.filter((e=>e!==t)),s({featuredAuctions:n})})(t.id,a)},t.id);var a}))})]}),e.enableFeaturedAuctions&&(0,r.jsx)("div",{className:"info-section",children:(0,r.jsxs)("p",{className:"help-text",children:["💡 ",(0,r.jsx)("strong",{children:"نصيحة:"})," المزادات المميزة ستظهر دائماً في الصف الأول مع تصميم مميز (إطار ذهبي ونجمة)."]})})]})]})},p=({attributes:e,setAttributes:s})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(o.PanelBody,{title:"إعدادات عرض المزادات",initialOpen:!0,children:[(0,r.jsx)(o.RangeControl,{label:"عدد الأعمدة في كل صف",value:e.columns,onChange:e=>s({columns:e}),min:1,max:6}),(0,r.jsx)(o.RangeControl,{label:"عدد المزادات المعروضة",value:e.auctionsCount,onChange:e=>s({auctionsCount:e}),min:1,max:30}),(0,r.jsx)(o.ToggleControl,{label:"إظهار الصور",checked:e.showImages,onChange:e=>s({showImages:e})}),(0,r.jsx)(o.ToggleControl,{label:"إظهار اسم المزاد",checked:e.showTitle,onChange:e=>s({showTitle:e})}),(0,r.jsx)(o.ToggleControl,{label:"إظهار نوع المزاد",checked:e.showType,onChange:e=>s({showType:e})}),(0,r.jsx)(o.ToggleControl,{label:"إظهار المكان",checked:e.showLocation,onChange:e=>s({showLocation:e})}),(0,r.jsx)(o.ToggleControl,{label:"إظهار عدد الأصول",checked:e.showAssetsCount,onChange:e=>s({showAssetsCount:e})}),(0,r.jsx)(o.ToggleControl,{label:"إظهار التاريخ والوقت",checked:e.showDateTime,onChange:e=>s({showDateTime:e})}),(0,r.jsx)(o.ToggleControl,{label:"إظهار العداد التنازلي",checked:e.showCountdown,onChange:e=>s({showCountdown:e})}),(0,r.jsx)(o.ToggleControl,{label:"إظهار اسم الوكيل",checked:e.showAgent,onChange:e=>s({showAgent:e})})]}),(0,r.jsx)(j,{attributes:e,setAttributes:s})]}),v=({auctions:e,columns:s,settings:t,loading:a})=>{if(a)return(0,r.jsxs)("div",{className:"amlak-auctions-loading",style:{textAlign:"center",padding:"40px"},children:[(0,r.jsx)(o.Spinner,{}),(0,r.jsx)("p",{style:{marginTop:"16px",color:"#666"},children:"جاري تحميل المزادات..."})]});if(!e||0===e.length)return(0,r.jsx)("div",{className:"amlak-auctions-empty",children:(0,r.jsx)("p",{children:"لا توجد مزادات متاحة حالياً"})});const{combined:n}=((e,s=[],t=!1)=>{if(!t||!s.length)return{featured:[],regular:e,combined:e};const a=[],n=[],i=new Set(s);e.forEach((e=>{i.has(e.id)?a.push({...e,isFeatured:!0}):n.push({...e,isFeatured:!1})}));const l=s.map((e=>a.find((s=>s.id===e)))).filter(Boolean);return{featured:l,regular:n,combined:[...l,...n]}})(e,t.featuredAuctions,t.enableFeaturedAuctions);return(0,r.jsx)("div",{className:"amlak-auctions-container",children:(0,r.jsx)("div",{className:"amlak-auctions-grid",style:{display:"grid",gridTemplateColumns:`repeat(${s}, 1fr)`,gap:"24px"},children:n.map(((e,a)=>{const n=((e,s=0,t=!1)=>{const a=["amlak-auction-card"];return e.isFeatured&&(a.push("featured-auction"),a.push("featured-auction-card")),t&&a.push("first-row-auction"),e.isFeatured&&t&&a.push("featured-first-row"),a.push(`auction-position-${s}`),a.join(" ")})(e,a,a<s);return(0,r.jsx)("div",{className:n,children:(0,r.jsx)(x,{auction:e,settings:t,isFeatured:e.isFeatured})},e.id)}))})})},w=JSON.parse('{"UU":"create-block/amlak-auctions-block"}');(0,a.registerBlockType)(w.UU,{edit:function({attributes:e,setAttributes:s}){const[t,a]=(0,n.useState)([]),[l,o]=(0,n.useState)(!0),[d,h]=(0,n.useState)(null),u=async()=>{try{o(!0),h(null);const s=await c()({path:`/amlak/v1/auctions?limit=${e.auctionsCount}&status=publish`});a(s||[])}catch(e){console.error("Error fetching auctions:",e),h("حدث خطأ في تحميل المزادات"),a([])}finally{o(!1)}};return(0,n.useEffect)((()=>{u()}),[e.auctionsCount]),d?(0,r.jsxs)("div",{...(0,i.useBlockProps)(),children:[(0,r.jsx)(i.InspectorControls,{children:(0,r.jsx)(p,{attributes:e,setAttributes:s})}),(0,r.jsxs)("div",{className:"amlak-auctions-error",children:[(0,r.jsx)("p",{children:d}),(0,r.jsx)("button",{onClick:u,children:"إعادة المحاولة"})]})]}):(0,r.jsxs)("div",{...(0,i.useBlockProps)(),children:[(0,r.jsx)(i.InspectorControls,{children:(0,r.jsx)(p,{attributes:e,setAttributes:s})}),(0,r.jsx)(v,{auctions:t,columns:e.columns,settings:e,loading:l})]})},save:function({attributes:e}){return(0,r.jsx)("div",{...i.useBlockProps.save(),"data-columns":e.columns,"data-auctions-count":e.auctionsCount,"data-show-images":e.showImages,"data-show-title":e.showTitle,"data-show-type":e.showType,"data-show-location":e.showLocation,"data-show-assets-count":e.showAssetsCount,"data-show-date-time":e.showDateTime,"data-show-countdown":e.showCountdown,"data-show-agent":e.showAgent,"data-enable-featured-auctions":e.enableFeaturedAuctions,"data-featured-auctions":JSON.stringify(e.featuredAuctions),children:(0,r.jsxs)("div",{className:"amlak-auctions-placeholder",children:[(0,r.jsx)("p",{children:"جاري تحميل المزادات..."}),e.enableFeaturedAuctions&&e.featuredAuctions.length>0&&(0,r.jsxs)("p",{className:"featured-placeholder",children:["⭐ مع ",e.featuredAuctions.length," مزاد مميز"]})]})})}})}},t={};function a(e){var n=t[e];if(void 0!==n)return n.exports;var i=t[e]={exports:{}};return s[e](i,i.exports,a),i.exports}a.m=s,e=[],a.O=(s,t,n,i)=>{if(!t){var l=1/0;for(d=0;d<e.length;d++){for(var[t,n,i]=e[d],c=!0,o=0;o<t.length;o++)(!1&i||l>=i)&&Object.keys(a.O).every((e=>a.O[e](t[o])))?t.splice(o--,1):(c=!1,i<l&&(l=i));if(c){e.splice(d--,1);var r=n();void 0!==r&&(s=r)}}return s}i=i||0;for(var d=e.length;d>0&&e[d-1][2]>i;d--)e[d]=e[d-1];e[d]=[t,n,i]},a.n=e=>{var s=e&&e.__esModule?()=>e.default:()=>e;return a.d(s,{a:s}),s},a.d=(e,s)=>{for(var t in s)a.o(s,t)&&!a.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:s[t]})},a.o=(e,s)=>Object.prototype.hasOwnProperty.call(e,s),(()=>{var e={263:0,351:0};a.O.j=s=>0===e[s];var s=(s,t)=>{var n,i,[l,c,o]=t,r=0;if(l.some((s=>0!==e[s]))){for(n in c)a.o(c,n)&&(a.m[n]=c[n]);if(o)var d=o(a)}for(s&&s(t);r<l.length;r++)i=l[r],a.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return a.O(d)},t=globalThis.webpackChunkamlak_auctions_block=globalThis.webpackChunkamlak_auctions_block||[];t.forEach(s.bind(null,0)),t.push=s.bind(null,t.push.bind(t))})();var n=a.O(void 0,[351],(()=>a(307)));n=a.O(n)})();