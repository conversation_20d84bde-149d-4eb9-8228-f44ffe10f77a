(()=>{function n(n,t){const e=(new Date).getTime(),a=new Date(t).getTime()-e;if(a<0)return void(n.innerHTML='<div class="countdown-expired">انتهى المزاد</div>');const s=Math.floor(a/864e5),i=Math.floor(a%864e5/36e5),o=Math.floor(a%36e5/6e4),c=Math.floor(a%6e4/1e3);n.innerHTML=`\n        <div class="countdown-timer">\n            <div class="countdown-item">\n                <span class="countdown-number">${s}</span>\n                <span class="countdown-label">يوم</span>\n            </div>\n            <div class="countdown-item">\n                <span class="countdown-number">${i}</span>\n                <span class="countdown-label">ساعة</span>\n            </div>\n            <div class="countdown-item">\n                <span class="countdown-number">${o}</span>\n                <span class="countdown-label">دقيقة</span>\n            </div>\n            <div class="countdown-item">\n                <span class="countdown-number">${c}</span>\n                <span class="countdown-label">ثانية</span>\n            </div>\n        </div>\n    `}document.addEventListener("DOMContentLoaded",(function(){document.querySelectorAll(".wp-block-create-block-amlak-auctions-block").forEach((t=>{!async function(t){try{const e={columns:parseInt(t.dataset.columns)||3,auctionsCount:parseInt(t.dataset.auctionsCount)||6,showImages:"true"===t.dataset.showImages,showTitle:"true"===t.dataset.showTitle,showType:"true"===t.dataset.showType,showLocation:"true"===t.dataset.showLocation,showAssetsCount:"true"===t.dataset.showAssetsCount,showDateTime:"true"===t.dataset.showDateTime,showCountdown:"true"===t.dataset.showCountdown,showAgent:"true"===t.dataset.showAgent,enableFeaturedAuctions:"true"===t.dataset.enableFeaturedAuctions,featuredAuctions:JSON.parse(t.dataset.featuredAuctions||"[]")},a=await fetch(`/wp-json/amlak/v1/auctions?limit=${e.auctionsCount}&status=publish`);if(!a.ok)throw new Error("Failed to fetch auctions");!function(t,e,a){if(!e||0===e.length)return void(t.innerHTML='\n            <div class="amlak-auctions-empty">\n                <p>لا توجد مزادات متاحة حالياً</p>\n            </div>\n        ');const s=function(n,t){if(!t.enableFeaturedAuctions||!t.featuredAuctions.length)return n.map((n=>({...n,isFeatured:!1})));const e=new Set(t.featuredAuctions),a=[],s=[];n.forEach((n=>{e.has(n.id)?a.push({...n,isFeatured:!0}):s.push({...n,isFeatured:!1})}));return[...t.featuredAuctions.map((n=>a.find((t=>t.id===n)))).filter(Boolean),...s]}(e,a),i=`\n        <div class="amlak-auctions-container">\n            ${a.enableFeaturedAuctions&&a.featuredAuctions.length>0?`\n        <div class="featured-auctions-info">\n            <p class="featured-info-text">\n                ⭐ يتم عرض ${a.featuredAuctions.length} مزاد مميز في المقدمة\n            </p>\n        </div>\n    `:""}\n            <div class="amlak-auctions-grid" style="display: grid; grid-template-columns: repeat(${a.columns}, 1fr); gap: 24px;">\n                ${s.map(((n,t)=>{const e=t<a.columns;return function(n,t,e=0,a=!1){const s=["amlak-auction-card","clickable-card"];n.isFeatured&&s.push("featured-auction","featured-auction-card"),a&&s.push("first-row-auction");const i=n.url?`onclick="window.location.href='${n.url}'"`:"",o=n.url?"cursor: pointer;":"",c=n.isFeatured?'\n        <div class="featured-badge crown-style animated">\n            <span class="featured-icon">👑</span>\n            <span class="featured-text">مميز</span>\n        </div>\n    ':"",d=t.showImages&&n.images&&n.images.length>0?`\n        <div class="auction-img-wrapper">\n            ${t.showType?`\n                <span class="auction-type-badge auction-type-${n.type}">\n                    <span class="badge-icon-bg badge-icon-bg-${n.type}">\n                        <span class="badge-icon-large">${{إلكتروني:'<svg width="22" height="22" viewBox="0 0 24 24" fill="white"><rect x="3" y="6" width="18" height="9" rx="2"/><rect x="7" y="16" width="10" height="2" rx="1"/></svg>',حضوري:'<svg width="22" height="22" viewBox="0 0 24 24" fill="white"><path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05C15.64 13.36 17 14.28 17 15.5V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/></svg>',هجين:'<svg width="22" height="22" viewBox="0 0 24 24" fill="white"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>'}[n.type]||""}</span>\n                    </span>\n                    <span class="badge-text-bg">${n.type}</span>\n                </span>\n            `:""}\n            <div class="img-slider">\n                <img src="${n.images[0]}" alt="${n.title}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 12px 12px 0 0;">\n            </div>\n        </div>\n    `:"",l=t.showTitle?`\n        <div class="auction-title-agent-row">\n            <h2 class="auction-title-new">${n.title}</h2>\n            ${t.showAgent&&n.agent?`<div class="auction-agent-new">${n.agent}</div>`:""}\n        </div>\n    `:"",u=t.showLocation&&n.location?`\n        <div class="auction-info-item">\n            <span class="icon">\n                <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 11.5A2.5 2.5 0 1112 8a2.5 2.5 0 010 5.5z"/></svg>\n            </span>\n            ${n.location}\n        </div>\n    `:"",r=t.showAssetsCount?`\n        <div class="auction-info-item">\n            <span class="icon">\n                <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M4 22V2h16v20h-7v-6h-2v6H4zm2-2h4v-6h4v6h4V4H6v16z"/></svg>\n            </span>\n            ${n.assetsCount} ${1===n.assetsCount?"أصل":"أصول"}\n        </div>\n    `:"",h=t.showDateTime&&n.date?`\n        <div class="auction-date-row">\n            <span class="icon">\n                <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11zm0-13H5V6h14v1zm-7 5h5v5h-5z"/></svg>\n            </span>\n            <span class="auction-date-text">${function(n,t){if(!n)return"";const e=new Date(n);return`${e.getDate()} ${["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"][e.getMonth()]} ${e.getFullYear()}${t?` - ${t}`:""}`}(n.date,n.time)}</span>\n        </div>\n    `:"",v=t.showCountdown&&n.endDateTime?`\n        <div class="countdown-container" data-end-time="${n.endDateTime}">\n            <div class="countdown-loading">جاري تحميل العد التنازلي...</div>\n        </div>\n    `:"";return`\n        <div class="${s.join(" ")}" ${i} style="${o}" role="button" tabindex="0">\n            ${c}\n            ${d}\n            ${l}\n            <div class="auction-info-grid">\n                ${u}\n                ${r}\n                ${h}\n            </div>\n            ${v}\n\n            ${n.url?'\n                <div class="card-click-overlay">\n                    <div class="click-indicator">\n                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">\n                            <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>\n                        </svg>\n                        <span>اضغط للعرض</span>\n                    </div>\n                </div>\n            ':""}\n        </div>\n    `}(n,a,t,e)})).join("")}\n            </div>\n        </div>\n    `;t.innerHTML=i,function(t){t.querySelectorAll(".countdown-container").forEach((t=>{const e=t.dataset.endTime;e&&(n(t,e),setInterval((()=>n(t,e)),1e3))}))}(t)}(t,await a.json(),e),function(n){n.querySelectorAll(".clickable-card").forEach((n=>{n.addEventListener("keydown",(function(t){if("Enter"===t.key||" "===t.key){t.preventDefault();const e=n.getAttribute("onclick");if(e){const n=e.match(/window\.location\.href='([^']+)'/);n&&n[1]&&(window.location.href=n[1])}}}))}))}(t)}catch(n){console.error("Error loading auctions:",n),t.innerHTML='\n            <div class="amlak-auctions-error">\n                <p>حدث خطأ في تحميل المزادات</p>\n                <button onclick="location.reload()">إعادة المحاولة</button>\n            </div>\n        '}}(t)}))}))})();