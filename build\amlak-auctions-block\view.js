(()=>{function n(n,t){const e=(new Date).getTime(),a=new Date(t).getTime()-e;if(a<0)return void(n.innerHTML='<div class="countdown-expired">انتهى المزاد</div>');const o=Math.floor(a/864e5),s=Math.floor(a%864e5/36e5),i=Math.floor(a%36e5/6e4),r=Math.floor(a%6e4/1e3);n.innerHTML=`\n        <div class="countdown-timer">\n            <div class="countdown-item">\n                <span class="countdown-number">${o}</span>\n                <span class="countdown-label">يوم</span>\n            </div>\n            <div class="countdown-item">\n                <span class="countdown-number">${s}</span>\n                <span class="countdown-label">ساعة</span>\n            </div>\n            <div class="countdown-item">\n                <span class="countdown-number">${i}</span>\n                <span class="countdown-label">دقيقة</span>\n            </div>\n            <div class="countdown-item">\n                <span class="countdown-number">${r}</span>\n                <span class="countdown-label">ثانية</span>\n            </div>\n        </div>\n    `}document.addEventListener("DOMContentLoaded",(function(){!function(){if(document.getElementById("featured-auctions-css"))return;const n=document.createElement("style");n.id="featured-auctions-css",n.textContent="\n        /* Featured badge - شارة شريط مائل أنيقة موحدة */\n        .featured-badge {\n            position: absolute;\n            z-index: 10;\n            top: 15px;\n            left: -10px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            width: 110px;\n            height: 30px;\n            background: linear-gradient(135deg, #ff6b35, #f7931e);\n            color: white;\n            font-size: 12px;\n            font-weight: 700;\n            text-transform: uppercase;\n            letter-spacing: 0.5px;\n            transform: rotate(-45deg);\n            box-shadow:\n                0 3px 10px rgba(255, 107, 53, 0.4),\n                0 1px 0 rgba(255, 255, 255, 0.2) inset;\n            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n            transition: all 0.3s ease;\n        }\n\n        .featured-badge:hover {\n            background: linear-gradient(135deg, #f7931e, #ff6b35);\n            box-shadow:\n                0 5px 15px rgba(255, 107, 53, 0.6),\n                0 1px 0 rgba(255, 255, 255, 0.3) inset;\n            transform: rotate(-45deg) scale(1.05);\n        }\n\n        .featured-badge::before {\n            content: '';\n            position: absolute;\n            top: -2px;\n            left: -2px;\n            right: -2px;\n            bottom: -2px;\n            background: linear-gradient(135deg, #ff8c42, #ff6b35);\n            transform: rotate(0deg);\n            z-index: -1;\n            border-radius: 2px;\n        }\n\n        .featured-badge::after {\n            content: '';\n            position: absolute;\n            top: 50%;\n            left: -12px;\n            width: 0;\n            height: 0;\n            border-top: 15px solid transparent;\n            border-bottom: 15px solid transparent;\n            border-right: 10px solid #d63031;\n            transform: translateY(-50%);\n        }\n\n        .featured-icon {\n            font-size: 12px;\n            line-height: 1;\n            margin-left: 4px;\n            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\n        }\n\n        .featured-text {\n            font-size: 10px;\n            font-weight: 700;\n            text-transform: uppercase;\n            letter-spacing: 0.5px;\n            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n            line-height: 1;\n        }\n\n        /* تأثيرات الحركة للشارة المائلة */\n        @keyframes ribbonFloat {\n            0%, 100% { transform: rotate(-45deg) translateY(0px); }\n            50% { transform: rotate(-45deg) translateY(-2px); }\n        }\n\n        .featured-badge {\n            animation: ribbonFloat 4s ease-in-out infinite;\n        }\n\n        /* Featured auction card styles */\n        .featured-auction-card {\n            position: relative;\n            overflow: visible;\n        }\n\n        .amlak-auctions-grid .featured-auction-card {\n            border: 2px solid #ff6b35 !important;\n            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3) !important;\n            transform: translateY(-2px);\n            transition: all 0.3s ease;\n            background: linear-gradient(135deg, #ffffff, #fffaf7);\n        }\n\n        .amlak-auctions-grid .featured-auction-card:hover {\n            transform: translateY(-4px);\n            box-shadow: 0 8px 20px rgba(255, 107, 53, 0.4) !important;\n        }\n\n        /* Responsive design */\n        @media (max-width: 768px) {\n            .featured-badge {\n                width: 90px;\n                height: 26px;\n                top: 12px;\n                left: -8px;\n                font-size: 11px;\n            }\n\n            .featured-icon {\n                font-size: 11px;\n                margin-left: 3px;\n            }\n\n            .featured-text {\n                font-size: 9px;\n                letter-spacing: 0.4px;\n            }\n\n            .featured-badge::after {\n                border-top: 13px solid transparent;\n                border-bottom: 13px solid transparent;\n                border-right: 8px solid #d63031;\n                left: -10px;\n            }\n        }\n\n        @media (max-width: 480px) {\n            .featured-badge {\n                width: 80px;\n                height: 24px;\n                top: 10px;\n                left: -7px;\n                font-size: 10px;\n            }\n\n            .featured-icon {\n                font-size: 10px;\n                margin-left: 2px;\n            }\n\n            .featured-text {\n                font-size: 8px;\n            }\n\n            .featured-badge::after {\n                border-top: 12px solid transparent;\n                border-bottom: 12px solid transparent;\n                border-right: 7px solid #d63031;\n                left: -9px;\n            }\n        }\n    ",document.head.appendChild(n)}(),document.querySelectorAll(".wp-block-create-block-amlak-auctions-block").forEach((t=>{!async function(t){try{const e={columns:parseInt(t.dataset.columns)||3,auctionsCount:parseInt(t.dataset.auctionsCount)||6,showImages:"true"===t.dataset.showImages,showTitle:"true"===t.dataset.showTitle,showType:"true"===t.dataset.showType,showLocation:"true"===t.dataset.showLocation,showAssetsCount:"true"===t.dataset.showAssetsCount,showDateTime:"true"===t.dataset.showDateTime,showCountdown:"true"===t.dataset.showCountdown,showAgent:"true"===t.dataset.showAgent,enableFeaturedAuctions:"true"===t.dataset.enableFeaturedAuctions,featuredAuctions:JSON.parse(t.dataset.featuredAuctions||"[]")},a=await fetch(`/wp-json/amlak/v1/auctions?limit=${e.auctionsCount}&status=publish`);if(!a.ok)throw new Error("Failed to fetch auctions");!function(t,e,a){if(!e||0===e.length)return void(t.innerHTML='\n            <div class="amlak-auctions-empty">\n                <p>لا توجد مزادات متاحة حالياً</p>\n            </div>\n        ');const o=function(n,t){if(!t.enableFeaturedAuctions||!t.featuredAuctions.length)return n.map((n=>({...n,isFeatured:!1})));const e=new Set(t.featuredAuctions),a=[],o=[];n.forEach((n=>{e.has(n.id)?a.push({...n,isFeatured:!0}):o.push({...n,isFeatured:!1})}));return[...t.featuredAuctions.map((n=>a.find((t=>t.id===n)))).filter(Boolean),...o]}(e,a),s=`\n        <div class="amlak-auctions-container">\n            <div class="amlak-auctions-grid" style="display: grid; grid-template-columns: repeat(${a.columns}, 1fr); gap: 24px;">\n                ${o.map(((n,t)=>{const e=t<a.columns;return function(n,t,e=0,a=!1){const o=["amlak-auction-card","clickable-card"];n.isFeatured&&o.push("featured-auction","featured-auction-card"),a&&o.push("first-row-auction");const s=n.url?`onclick="window.location.href='${n.url}'"`:"",i=n.url?"cursor: pointer;":"",r=n.isFeatured?'\n        <div class="featured-badge">\n            <span class="featured-icon">⭐</span>\n            <span class="featured-text">مميز</span>\n        </div>\n    ':"",d=t.showImages&&n.images&&n.images.length>0?`\n        <div class="auction-img-wrapper">\n            ${t.showType?`\n                <span class="auction-type-badge auction-type-${n.type}">\n                    <span class="badge-icon-bg badge-icon-bg-${n.type}">\n                        <span class="badge-icon-large">${{إلكتروني:'<svg width="22" height="22" viewBox="0 0 24 24" fill="white"><rect x="3" y="6" width="18" height="9" rx="2"/><rect x="7" y="16" width="10" height="2" rx="1"/></svg>',حضوري:'<svg width="22" height="22" viewBox="0 0 24 24" fill="white"><path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05C15.64 13.36 17 14.28 17 15.5V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/></svg>',هجين:'<svg width="22" height="22" viewBox="0 0 24 24" fill="white"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>'}[n.type]||""}</span>\n                    </span>\n                    <span class="badge-text-bg">${n.type}</span>\n                </span>\n            `:""}\n            <div class="img-slider">\n                <img src="${n.images[0]}" alt="${n.title}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 12px 12px 0 0;">\n            </div>\n        </div>\n    `:"",c=t.showTitle?`\n        <div class="auction-title-agent-row">\n            <h2 class="auction-title-new">${n.title}</h2>\n            ${t.showAgent&&n.agent?`<div class="auction-agent-new">${n.agent}</div>`:""}\n        </div>\n    `:"",l=t.showLocation&&n.location?`\n        <div class="auction-info-item">\n            <span class="icon">\n                <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 11.5A2.5 2.5 0 1112 8a2.5 2.5 0 010 5.5z"/></svg>\n            </span>\n            ${n.location}\n        </div>\n    `:"",u=t.showAssetsCount?`\n        <div class="auction-info-item">\n            <span class="icon">\n                <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M4 22V2h16v20h-7v-6h-2v6H4zm2-2h4v-6h4v6h4V4H6v16z"/></svg>\n            </span>\n            ${n.assetsCount} ${1===n.assetsCount?"أصل":"أصول"}\n        </div>\n    `:"",p=t.showDateTime&&n.date?`\n        <div class="auction-date-row">\n            <span class="icon">\n                <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11zm0-13H5V6h14v1zm-7 5h5v5h-5z"/></svg>\n            </span>\n            <span class="auction-date-text">${function(n,t){if(!n)return"";const e=new Date(n);return`${e.getDate()} ${["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"][e.getMonth()]} ${e.getFullYear()}${t?` - ${t}`:""}`}(n.date,n.time)}</span>\n        </div>\n    `:"",h=t.showCountdown&&n.endDateTime?`\n        <div class="countdown-container" data-end-time="${n.endDateTime}">\n            <div class="countdown-loading">جاري تحميل العد التنازلي...</div>\n        </div>\n    `:"";return`\n        <div class="${o.join(" ")}" ${s} style="${i}" role="button" tabindex="0">\n            ${r}\n            ${d}\n            ${c}\n            <div class="auction-info-grid">\n                ${l}\n                ${u}\n                ${p}\n            </div>\n            ${h}\n\n            ${n.url?'\n                <div class="card-click-overlay">\n                    <div class="click-indicator">\n                        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">\n                            <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>\n                        </svg>\n                        <span>اضغط للعرض</span>\n                    </div>\n                </div>\n            ':""}\n        </div>\n    `}(n,a,t,e)})).join("")}\n            </div>\n        </div>\n    `;t.innerHTML=s,function(t){t.querySelectorAll(".countdown-container").forEach((t=>{const e=t.dataset.endTime;e&&(n(t,e),setInterval((()=>n(t,e)),1e3))}))}(t)}(t,await a.json(),e),function(n){n.querySelectorAll(".clickable-card").forEach((n=>{n.addEventListener("keydown",(function(t){if("Enter"===t.key||" "===t.key){t.preventDefault();const e=n.getAttribute("onclick");if(e){const n=e.match(/window\.location\.href='([^']+)'/);n&&n[1]&&(window.location.href=n[1])}}}))}))}(t)}catch(n){console.error("Error loading auctions:",n),t.innerHTML='\n            <div class="amlak-auctions-error">\n                <p>حدث خطأ في تحميل المزادات</p>\n                <button onclick="location.reload()">إعادة المحاولة</button>\n            </div>\n        '}}(t)}))}))})();