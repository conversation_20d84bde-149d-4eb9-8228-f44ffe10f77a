/* Featured Auctions Manager Styles */
.featured-auctions-manager {
    margin-top: 16px;
}

/* Search Section */
.search-section {
    margin-bottom: 20px;
}

.search-section .components-text-control__input {
    border-radius: 6px;
    border: 1px solid #ddd;
    padding: 8px 12px;
    font-size: 14px;
}

.search-section .components-text-control__input:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 1px #007cba;
}

/* Section Titles */
.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #1e1e1e;
    margin: 16px 0 12px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;
}

/* Selected Auctions Section */
.selected-auctions-section {
    background: #f8f9fa;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.selected-auctions-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
}

.selected-auction-tag {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border: 1px solid #f59e0b;
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 12px;
    color: #92400e;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.1);
}

.selected-auction-tag .tag-content {
    display: flex;
    flex-direction: column;
    margin-right: 8px;
}

.selected-auction-tag .tag-content strong {
    font-weight: 600;
    line-height: 1.2;
}

.selected-auction-tag .tag-content small {
    font-size: 11px;
    opacity: 0.8;
    margin-top: 2px;
}

.remove-tag-btn {
    background: rgba(239, 68, 68, 0.1) !important;
    color: #dc2626 !important;
    border: none !important;
    border-radius: 50% !important;
    width: 20px !important;
    height: 20px !important;
    min-width: 20px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 12px !important;
    line-height: 1 !important;
    margin-left: 4px;
}

.remove-tag-btn:hover {
    background: rgba(239, 68, 68, 0.2) !important;
    transform: scale(1.1);
}

/* Available Auctions Section */
.available-auctions-section {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.available-auctions-section .section-title {
    background: #f8f9fa;
    margin: 0;
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 0;
}

/* Loading and Error States */
.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    gap: 12px;
    color: #666;
}

.error-container {
    padding: 20px;
    text-align: center;
    background: #fef2f2;
    border: 1px solid #fecaca;
    margin: 16px;
    border-radius: 6px;
}

.error-message {
    color: #dc2626;
    margin-bottom: 12px;
    font-size: 14px;
}

/* Auctions List */
.auctions-list {
    max-height: 400px;
    overflow-y: auto;
    border-radius: 0 0 8px 8px;
}

.auctions-list::-webkit-scrollbar {
    width: 6px;
}

.auctions-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.auctions-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.auctions-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.no-results {
    padding: 40px 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Auction Option */
.auction-option {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    cursor: pointer;
}

.auction-option:hover {
    background: #f8f9fa;
}

.auction-option.selected {
    background: #e6f3ff;
    border-left: 3px solid #007cba;
}

.auction-option:last-child {
    border-bottom: none;
}

.auction-option .components-checkbox-control__input-container {
    margin-right: 12px;
    margin-top: 2px;
}

.auction-info {
    flex: 1;
    min-width: 0;
}

.auction-title {
    font-weight: 600;
    color: #1e1e1e;
    margin-bottom: 6px;
    line-height: 1.3;
}

.auction-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    font-size: 12px;
    color: #666;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

/* Info Section */
.info-section {
    margin-top: 20px;
    padding: 16px;
    background: #e6f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
}

.help-text {
    margin: 0;
    font-size: 13px;
    color: #0073aa;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .selected-auctions-tags {
        flex-direction: column;
    }
    
    .selected-auction-tag {
        justify-content: space-between;
    }
    
    .auction-meta {
        flex-direction: column;
        gap: 4px;
    }
    
    .meta-item {
        justify-content: flex-start;
    }
}

/* Animation for new selections */
.auction-option.selected {
    animation: selectHighlight 0.3s ease;
}

@keyframes selectHighlight {
    0% {
        background: #007cba;
        color: white;
    }
    100% {
        background: #e6f3ff;
        color: inherit;
    }
}

/* Featured badge for selected items in list */
.auction-option.selected .auction-title::after {
    content: " ⭐";
    color: #ffd700;
    font-size: 14px;
    margin-left: 4px;
}
