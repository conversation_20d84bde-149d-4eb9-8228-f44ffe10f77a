import React, { useEffect, useState } from 'react';
import { InspectorControls, useBlockProps } from '@wordpress/block-editor';
import apiFetch from '@wordpress/api-fetch';
import { Spinner } from '@wordpress/components';
import Card from './Card';
import SettingsPanel from './SettingsPanel';
import { separateAuctions, getAuctionCardClasses } from './FeaturedAuctionsLogic';
import './style.scss';

const AuctionsGrid = ({ auctions, columns, settings, loading }) => {
    if (loading) {
        return (
            <div className="amlak-auctions-loading" style={{ textAlign: 'center', padding: '40px' }}>
                <Spinner />
                <p style={{ marginTop: '16px', color: '#666' }}>جاري تحميل المزادات...</p>
            </div>
        );
    }

    if (!auctions || auctions.length === 0) {
        return (
            <div className="amlak-auctions-empty">
                <p>لا توجد مزادات متاحة حالياً</p>
            </div>
        );
    }

    // فصل المزادات إلى مميزة وعادية
    const { combined: sortedAuctions } = separateAuctions(
        auctions,
        settings.featuredAuctions,
        settings.enableFeaturedAuctions
    );

    return (
        <div className="amlak-auctions-container">
            {/* عرض معلومات المزادات المميزة إذا كانت مفعلة */}
            {settings.enableFeaturedAuctions && settings.featuredAuctions.length > 0 && (
                <div className="featured-auctions-info">
                    <p className="featured-info-text">
                        ⭐ يتم عرض {settings.featuredAuctions.length} مزاد مميز في المقدمة
                    </p>
                </div>
            )}

            <div
                className="amlak-auctions-grid"
                style={{
                    display: 'grid',
                    gridTemplateColumns: `repeat(${columns}, 1fr)`,
                    gap: '24px',
                }}
            >
                {sortedAuctions.map((auction, index) => {
                    const isFirstRow = index < columns;
                    const cardClasses = getAuctionCardClasses(auction, index, isFirstRow);

                    return (
                        <div key={auction.id} className={cardClasses}>
                            <Card
                                auction={auction}
                                settings={settings}
                                isFeatured={auction.isFeatured}
                            />
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default function Edit({ attributes, setAttributes }) {
    const [auctions, setAuctions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Fetch auctions from REST API
    const fetchAuctions = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await apiFetch({
                path: `/amlak/v1/auctions?limit=${attributes.auctionsCount}&status=publish`,
            });

            setAuctions(response || []);
        } catch (err) {
            console.error('Error fetching auctions:', err);
            setError('حدث خطأ في تحميل المزادات');
            setAuctions([]);
        } finally {
            setLoading(false);
        }
    };

    // Fetch auctions on component mount and when auctionsCount changes
    useEffect(() => {
        fetchAuctions();
    }, [attributes.auctionsCount]);

    // Show error message if there's an error
    if (error) {
        return (
            <div {...useBlockProps()}>
                <InspectorControls>
                    <SettingsPanel attributes={attributes} setAttributes={setAttributes} />
                </InspectorControls>
                <div className="amlak-auctions-error">
                    <p>{error}</p>
                    <button onClick={fetchAuctions}>
                        إعادة المحاولة
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div {...useBlockProps()}>
            <InspectorControls>
                <SettingsPanel attributes={attributes} setAttributes={setAttributes} />
            </InspectorControls>
            <AuctionsGrid
                auctions={auctions}
                columns={attributes.columns}
                settings={attributes}
                loading={loading}
            />
        </div>
    );
}
