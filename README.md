# Amlak Auctions Block 🏠

بلاجن WordPress لعرض المزادات العقارية مع إمكانيات متقدمة وتصميم احترافي.

## 🌟 المميزات الرئيسية

### 🎯 **عرض المزادات**
- عرض شبكي قابل للتخصيص (1-6 أعمدة)
- معرض صور تفاعلي مع تأثيرات انتقال
- عد تنازلي للمزادات
- معلومات شاملة (الموقع، عدد الأصول، التاريخ، الوكيل)
- أنواع مزادات مختلفة (إلكتروني، حضوري، هجين)

### ⭐ **المزادات المميزة**
- شارة مائلة أنيقة للمزادات المميزة
- عرض المزادات المميزة في المقدمة دائماً
- واجهة بحث ذكية لاختيار المزادات
- تأثيرات بصرية متقدمة
- تصميم متجاوب

### 🎨 **التصميم**
- تصميم عصري ومتجاوب
- دعم RTL (العربية)
- ألوان وأنماط قابلة للتخصيص
- تأثيرات تفاعلية عند الحوم
- تحسين لإمكانية الوصول

## 📁 هيكل المشروع

```
src/amlak-auctions-block/
├── 📁 components/          # المكونات
│   ├── Card.js            # مكون الكارت
│   ├── Countdown.js       # العد التنازلي
│   ├── ImgSlider.js       # معرض الصور
│   ├── FeaturedAuctionsManager.js  # مدير المزادات المميزة
│   └── SettingsPanel.js   # لوحة الإعدادات
├── 📁 styles/             # الأنماط
│   ├── Card.css
│   ├── Countdown.css
│   ├── ImgSlider.css
│   ├── FeaturedAuctionsManager.css
│   └── FeaturedAuctions.css
├── 📁 utils/              # الأدوات المساعدة
│   ├── FeaturedAuctionsLogic.js
│   └── FeaturedBadgeStyles.js
├── edit.js                # محرر البلوك
├── save.js                # حفظ البلوك
├── view.js                # الواجهة الأمامية
├── style.scss             # أنماط عامة
└── block.json             # إعدادات البلوك
```

## 🚀 التثبيت والاستخدام

### التثبيت:
1. ارفع مجلد البلاجن إلى `/wp-content/plugins/`
2. فعّل البلاجن من لوحة تحكم WordPress
3. أضف البلوك "Amlak Auctions Block" في محرر الصفحات

### الاستخدام:
1. **إضافة البلوك**: ابحث عن "Amlak Auctions Block" في محرر البلوك
2. **تخصيص الإعدادات**: استخدم الشريط الجانبي لتخصيص العرض
3. **المزادات المميزة**: فعّل الخاصية واختر المزادات من القائمة

## ⚙️ إعدادات البلوك

### إعدادات العرض:
- **عدد الأعمدة**: 1-6 أعمدة
- **عدد المزادات**: 1-30 مزاد
- **إظهار/إخفاء العناصر**: الصور، العنوان، النوع، الموقع، إلخ

### إعدادات المزادات المميزة:
- **تفعيل الخاصية**: تشغيل/إيقاف المزادات المميزة
- **اختيار المزادات**: واجهة بحث وتحديد متقدمة
- **معاينة المختارة**: عرض المزادات المختارة مع إمكانية الحذف

## 🎨 التخصيص

### تخصيص الألوان:
```css
.featured-badge {
    background: linear-gradient(135deg, #your-color-1, #your-color-2);
}
```

### تخصيص الحجم:
```css
.featured-badge {
    width: 120px;  /* عرض مخصص */
    height: 35px;  /* ارتفاع مخصص */
}
```

## 🔧 التطوير

### البناء:
```bash
npm install
npm run build
```

### التطوير:
```bash
npm run start
```

### الاختبار:
```bash
npm run test
```

## 📊 متطلبات النظام

- **WordPress**: 5.0 أو أحدث
- **PHP**: 7.4 أو أحدث
- **Node.js**: 14 أو أحدث (للتطوير)

## 🐛 استكشاف الأخطاء

### المزادات لا تظهر:
1. تحقق من إعدادات REST API
2. تأكد من وجود مزادات منشورة
3. راجع console المتصفح للأخطاء

### الشارة المميزة لا تظهر:
1. تأكد من تفعيل الخاصية
2. تحقق من اختيار المزادات
3. امسح cache المتصفح

## 📚 الوثائق التفصيلية

- [`FEATURED_AUCTIONS_README.md`](./FEATURED_AUCTIONS_README.md) - دليل المزادات المميزة
- [`NEW_STRUCTURE_GUIDE.md`](./NEW_STRUCTURE_GUIDE.md) - دليل هيكل المشروع

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة GPL v2 أو أحدث.

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

---

**تم تطويره بـ ❤️ لمجتمع العقارات العربي**
