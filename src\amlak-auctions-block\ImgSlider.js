import React, { useState, useEffect, useRef } from 'react';
import './ImgSlider.css';

const AUTO_SLIDE_INTERVAL = 3000; // 3 ثواني

const ImgSlider = ({ images = [] }) => {
    const [current, setCurrent] = useState(0);
    const [paused, setPaused] = useState(false);
    const intervalRef = useRef();
    if (!images.length) return null;

    // التقليب التلقائي
    useEffect(() => {
        if (paused) return;
        intervalRef.current = setInterval(() => {
            setCurrent((prev) => (prev + 1) % images.length);
        }, AUTO_SLIDE_INTERVAL);
        return () => clearInterval(intervalRef.current);
    }, [images.length, paused]);

    // عند تمرير الماوس توقف التقليب
    const handleMouseEnter = () => setPaused(true);
    const handleMouseLeave = () => setPaused(false);

    return (
        <div className="amlak-img-slider" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
            <div className="slider-img-fade-wrapper">
                {images.map((img, idx) => (
                    <img
                        key={idx}
                        src={img}
                        alt="صورة المزاد"
                        className={
                            'slider-img-fade' + (idx === current ? ' active' : '')
                        }
                        style={{ zIndex: idx === current ? 2 : 1 }}
                    />
                ))}
            </div>
            <div className="slider-dots">
                {images.map((_, idx) => (
                    <span
                        key={idx}
                        className={idx === current ? 'dot active' : 'dot'}
                        onClick={() => setCurrent(idx)}
                    />
                ))}
            </div>
        </div>
    );
};

export default ImgSlider;
