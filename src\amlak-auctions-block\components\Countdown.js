import React, { useEffect, useState } from 'react';
import '../styles/Countdown.css';

const getTimeLeft = (endDateTime) => {
    const total = Date.parse(endDateTime) - Date.now();
    const seconds = Math.floor((total / 1000) % 60);
    const minutes = Math.floor((total / 1000 / 60) % 60);
    const hours = Math.floor((total / (1000 * 60 * 60)) % 24);
    const days = Math.floor(total / (1000 * 60 * 60 * 24));
    return { total, days, hours, minutes, seconds };
};

const Countdown = ({ endDateTime }) => {
    const [timeLeft, setTimeLeft] = useState(getTimeLeft(endDateTime));

    useEffect(() => {
        const timer = setInterval(() => {
            setTimeLeft(getTimeLeft(endDateTime));
        }, 1000);
        return () => clearInterval(timer);
    }, [endDateTime]);

    if (timeLeft.total <= 0) {
        return <div className="amlak-countdown">انتهى المزاد</div>;
    }

    // تصميم العداد كبطاقات مستطيلة أفقية فقط
    return (
        <div className="amlak-countdown-cards-row">
            <div className="countdown-rects">
                <div className="countdown-rect"><div className="countdown-num">{timeLeft.days}</div><div className="countdown-label">يوم</div></div>
                <div className="countdown-rect"><div className="countdown-num">{timeLeft.hours}</div><div className="countdown-label">ساعة</div></div>
                <div className="countdown-rect"><div className="countdown-num">{timeLeft.minutes}</div><div className="countdown-label">دقيقة</div></div>
                <div className="countdown-rect"><div className="countdown-num">{timeLeft.seconds}</div><div className="countdown-label">ثانية</div></div>
            </div>
        </div>
    );
};

export default Countdown;
