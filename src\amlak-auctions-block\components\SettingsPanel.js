import React from 'react';
import { PanelBody, ToggleControl, RangeControl } from '@wordpress/components';
import FeaturedAuctionsManager from './FeaturedAuctionsManager';

const SettingsPanel = ({ attributes, setAttributes }) => {
    return (
        <>
            <PanelBody title="إعدادات عرض المزادات" initialOpen={true}>
                <RangeControl
                    label="عدد الأعمدة في كل صف"
                    value={attributes.columns}
                    onChange={(val) => setAttributes({ columns: val })}
                    min={1}
                    max={6}
                />
                <RangeControl
                    label="عدد المزادات المعروضة"
                    value={attributes.auctionsCount}
                    onChange={(val) => setAttributes({ auctionsCount: val })}
                    min={1}
                    max={30}
                />
                <ToggleControl
                    label="إظهار الصور"
                    checked={attributes.showImages}
                    onChange={(val) => setAttributes({ showImages: val })}
                />
                <ToggleControl
                    label="إظهار اسم المزاد"
                    checked={attributes.showTitle}
                    onChange={(val) => setAttributes({ showTitle: val })}
                />
                <ToggleControl
                    label="إظهار نوع المزاد"
                    checked={attributes.showType}
                    onChange={(val) => setAttributes({ showType: val })}
                />
                <ToggleControl
                    label="إظهار المكان"
                    checked={attributes.showLocation}
                    onChange={(val) => setAttributes({ showLocation: val })}
                />
                <ToggleControl
                    label="إظهار عدد الأصول"
                    checked={attributes.showAssetsCount}
                    onChange={(val) => setAttributes({ showAssetsCount: val })}
                />
                <ToggleControl
                    label="إظهار التاريخ والوقت"
                    checked={attributes.showDateTime}
                    onChange={(val) => setAttributes({ showDateTime: val })}
                />
                <ToggleControl
                    label="إظهار العداد التنازلي"
                    checked={attributes.showCountdown}
                    onChange={(val) => setAttributes({ showCountdown: val })}
                />
                <ToggleControl
                    label="إظهار اسم الوكيل"
                    checked={attributes.showAgent}
                    onChange={(val) => setAttributes({ showAgent: val })}
                />
            </PanelBody>

            {/* مدير المزادات المميزة */}
            <FeaturedAuctionsManager
                attributes={attributes}
                setAttributes={setAttributes}
            />
        </>
    );
};

export default SettingsPanel;
